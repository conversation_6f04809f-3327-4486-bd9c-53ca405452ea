# نظام إلغاء الطلبات وتنظيف اهتمامات السائقين التلقائي

## الوصف
تم إضافة نظام تلقائي لـ:
1. **إلغاء الطلبات المنتهية الصلاحية**: فحص الطلبات ذات الحالة `pending` وإلغاؤها تلقائياً إذا تجاوزت الوقت المحدد
2. **تنظيف اهتمامات السائقين المنتهية الصلاحية**: حذف السجلات من جدول `order_drivers` التي لا تحتوي على `order_id` وتجاوزت الوقت المحدد

## كيفية العمل

### 1. الإعداد
- يتم تحديد الوقت المسموح للتسليم من خلال إعداد `delivery_deadline_minutes` في جدول الإعدادات
- القيمة الافتراضية هي 30 دقيقة
- يمكن تغيير هذه القيمة من لوحة الإعدادات في الإدارة

### 2. المعالجة التلقائية

#### إلغاء الطلبات:
- يتم تشغيل فحص تلقائي كل دقيقة للطلبات المنتهية الصلاحية
- الطلبات التي تكون في حالة `pending` وتجاوزت الوقت المحدد يتم تغيير حالتها إلى `canceled_timed_out_by_system`
- يتم إضافة سجل tracking جديد يوضح سبب الإلغاء

#### تنظيف اهتمامات السائقين:
- يتم فحص جدول `order_drivers` للسجلات التي لا تحتوي على `order_id` (اهتمامات بدون طلبات مخصصة)
- السجلات التي تجاوزت الوقت المحدد من تاريخ إنشائها يتم حذفها تلقائياً
- هذا يساعد في تنظيف قاعدة البيانات من الاهتمامات القديمة غير المستخدمة

### 3. الملفات المضافة/المعدلة

#### الملفات الجديدة:
- `app/Console/Commands/CancelExpiredOrders.php` - الأمر المسؤول عن إلغاء الطلبات المنتهية الصلاحية

#### الملفات المعدلة:
- `app/Services/OrderService.php` - إضافة دالة `cancelExpiredOrders()`
- `routes/console.php` - إضافة المهمة المجدولة

## الاستخدام

### تشغيل الأمر يدوياً
```bash
php artisan orders:cancel-expired
```

### المهمة المجدولة
المهمة تعمل تلقائياً كل دقيقة. للتأكد من تشغيل المهام المجدولة، تأكد من تشغيل:
```bash
php artisan schedule:work
```

أو إضافة cron job:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### عرض المهام المجدولة
```bash
php artisan schedule:list
```

## الإعدادات

### تغيير وقت انتهاء الصلاحية
يمكن تغيير قيمة `delivery_deadline_minutes` من:
1. لوحة الإعدادات في الإدارة
2. أو مباشرة في قاعدة البيانات:
```sql
UPDATE settings SET value = '60' WHERE key = 'delivery_deadline_minutes';
```

## السجلات
- يتم تسجيل عمليات الإلغاء في ملف السجلات
- يتم إضافة سجل tracking لكل طلب ملغى يوضح سبب الإلغاء
- رسالة الإلغاء: "تم إلغاء الطلب تلقائياً بسبب انتهاء الوقت المحدد للتسليم"

## الاختبار
تم اختبار النظام وهو يعمل بشكل صحيح:

### إلغاء الطلبات:
- تم إلغاء 14 طلب منتهي الصلاحية في الاختبار الأول
- المهمة المجدولة تعمل كل دقيقة
- سجلات التتبع تُضاف بشكل صحيح

### تنظيف اهتمامات السائقين:
- تم حذف 1 اهتمام سائق منتهي الصلاحية في الاختبار
- جميع السجلات المنتهية الصلاحية تم تنظيفها بنجاح
- النظام يعمل بدون أخطاء
