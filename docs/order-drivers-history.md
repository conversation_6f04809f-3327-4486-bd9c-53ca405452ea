# نظام تاريخ اهتمامات السائقين (Order Drivers History)

## الوصف

تم إضافة نظام لحفظ تاريخ اهتمامات السائقين المحذوفة في جدول منفصل `order_drivers_history` لتتبع الطلبات التي تم إلغاؤها وأسباب الإلغاء.

## الميزات المضافة

### 1. جدول order_drivers_history

جدول جديد لحفظ سجلات اهتمامات السائقين المحذوفة مع المعلومات التالية:

- **original_order_driver_id**: معرف السجل الأصلي في جدول order_drivers
- **order_id**: معرف الطلب (إن وجد)
- **driver_id**: معرف السائق
- **type_id**: معرف نوع الشحنة
- **from_station_id**: معرف محطة الانطلاق
- **to_station_id**: معرف محطة الوصول
- **assigned_at**: تاريخ التخصيص
- **completed_at**: تاريخ الإكمال (إن وجد)
- **notes**: ملاحظات
- **cancellation_reason**: سبب الإلغاء
  - `expired_by_system`: تم الإلغاء تلقائياً بسبب انتهاء الوقت
  - `cancelled_by_driver`: تم الإلغاء من قبل السائق
  - `cancelled_by_admin`: تم الإلغاء من قبل الإدارة
- **cancellation_note**: ملاحظة إضافية حول الإلغاء
- **cancelled_at**: تاريخ الإلغاء
- **original_created_at**: تاريخ الإنشاء الأصلي
- **original_updated_at**: تاريخ التحديث الأصلي

### 2. موديل OrderDriverHistory

موديل Laravel جديد للتعامل مع جدول التاريخ مع:

- العلاقات مع الجداول الأخرى (User, Type, Station, Order)
- خاصية `getCancellationReasonLabelAttribute()` لعرض سبب الإلغاء بالعربية
- إعدادات الحقول والتواريخ

### 3. تحديث OrderService

#### دالة saveOrderDriverToHistory (خاصة)

دالة خاصة لحفظ سجل OrderDriver في التاريخ قبل حذفه:

```php
private function saveOrderDriverToHistory(OrderDriver $orderDriver, string $reason, string $note = null): void
```

#### دالة cancelDriverInterest (عامة)

دالة جديدة للسماح للسائق بإلغاء اهتمامه يدوياً:

```php
public function cancelDriverInterest(int $orderDriverId, int $driverId, string $reason = 'cancelled_by_driver', string $note = null): bool
```

**الشروط:**
- يجب أن يكون السائق هو صاحب الاهتمام
- يجب ألا يكون الطلب مخصص بالفعل (order_id = null)
- يجب أن يكون ضمن الوقت المسموح (delivery_deadline_minutes)

#### تحديث cleanExpiredDriverInterests

تم تحديث الدالة لحفظ السجلات في التاريخ قبل حذفها تلقائياً.

### 4. API جديد للإلغاء

#### Endpoint: DELETE /api/delivery/cancel-interest

يسمح للسائق بإلغاء اهتمامه قبل انتهاء الوقت المسموح.

**المعاملات:**
- `order_driver_id` (مطلوب): معرف اهتمام السائق
- `cancellation_note` (اختياري): ملاحظة الإلغاء (حد أقصى 500 حرف)

**مثال على الطلب:**
```json
{
    "order_driver_id": 123,
    "cancellation_note": "لا أستطيع التوصيل اليوم"
}
```

**مثال على الاستجابة الناجحة:**
```json
{
    "success": true,
    "message": "تم إلغاء الاهتمام بنجاح",
    "data": {
        "cancelled": true
    }
}
```

**مثال على الاستجابة عند الخطأ:**
```json
{
    "success": false,
    "message": "لا يمكن إلغاء الاهتمام بعد انتهاء الوقت المسموح"
}
```

### 5. Request Validation

#### CancelDriverInterestRequest

فئة validation جديدة للتحقق من:

- صحة معرف اهتمام السائق
- ملكية السائق للاهتمام
- عدم تخصيص الطلب بالفعل
- كون الإلغاء ضمن الوقت المسموح

## كيفية الاستخدام

### للسائقين (عبر API)

1. **عرض الاهتمامات الحالية**: استخدم `/api/orders` للحصول على قائمة الاهتمامات
2. **إلغاء اهتمام**: استخدم `DELETE /api/delivery/cancel-interest` مع معرف الاهتمام

### للإدارة

1. **عرض التاريخ**: يمكن الوصول لجدول `order_drivers_history` لعرض السجلات المحذوفة
2. **التقارير**: استخدام البيانات لإنشاء تقارير عن أسباب الإلغاء

### للمطورين

```php
// إلغاء اهتمام سائق يدوياً
$orderService = new OrderService();
$result = $orderService->cancelDriverInterest(
    $orderDriverId,
    $driverId,
    'cancelled_by_admin',
    'تم الإلغاء بناءً على طلب الإدارة'
);

// عرض تاريخ اهتمامات سائق معين
$history = OrderDriverHistory::where('driver_id', $driverId)
    ->with(['type', 'fromStation', 'toStation'])
    ->orderBy('cancelled_at', 'desc')
    ->get();
```

## الملفات المضافة/المعدلة

### الملفات الجديدة:
- `database/migrations/2025_09_28_112656_create_order_drivers_history_table.php`
- `app/Models/OrderDriverHistory.php`
- `app/Http/Requests/Api/CancelDriverInterestRequest.php`
- `app/Http/Requests/Api/CancelOrderRequest.php`

### الملفات المعدلة:
- `app/Services/OrderService.php` - إضافة دوال جديدة
- `app/Http/Controllers/Api/DeliverySearchController.php` - إضافة API الإلغاء
- `app/Http/Controllers/Api/OrderController.php` - إضافة API إلغاء الطلب
- `routes/api.php` - إضافة routes جديدة

## الفوائد

1. **تتبع كامل**: حفظ جميع الاهتمامات المحذوفة مع أسباب الحذف
2. **مرونة للسائقين**: إمكانية إلغاء الاهتمام قبل انتهاء الوقت
3. **تقارير مفصلة**: إمكانية تحليل أسباب الإلغاء وتحسين النظام
4. **شفافية**: تتبع جميع العمليات مع التواريخ والأسباب
5. **أمان**: التحقق من الصلاحيات والأوقات المسموحة

## API إلغاء الطلب من قبل صاحب الطلب

### Endpoint: POST /api/orders/cancel

يسمح لصاحب الطلب بإلغاء طلبه قبل انتهاء الوقت المسموح وإذا كان في حالة `pending`.

**الشروط:**
- يجب أن يكون المستخدم هو صاحب الطلب
- يجب أن تكون حالة الطلب `pending`
- يجب أن يكون ضمن الوقت المسموح (delivery_deadline_minutes)

**المعاملات:**
- `order_id` (مطلوب): معرف الطلب
- `cancellation_note` (اختياري): ملاحظة الإلغاء (حد أقصى 500 حرف)

**مثال على الطلب:**
```json
{
    "order_id": 123,
    "cancellation_note": "لا أحتاج الطلب الآن"
}
```

**مثال على الاستجابة الناجحة:**
```json
{
    "success": true,
    "message": "تم إلغاء الطلب بنجاح",
    "data": {
        "cancelled": true
    }
}
```

**مثال على الاستجابة عند الخطأ:**
```json
{
    "success": false,
    "message": "لا يمكن إلغاء الطلب بعد انتهاء الوقت المسموح"
}
```

### Request Validation

#### CancelOrderRequest

فئة validation جديدة للتحقق من:

- صحة معرف الطلب
- ملكية المستخدم للطلب
- كون حالة الطلب `pending`
- كون الإلغاء ضمن الوقت المسموح

### ما يحدث عند إلغاء الطلب:

1. **إذا كان هناك سائق مخصص للطلب:**
   - يتم حفظ سجل OrderDriver في التاريخ مع سبب `cancelled_by_admin`
   - يتم حذف سجل OrderDriver الأصلي

2. **تحديث حالة الطلب:**
   - يتم تغيير حالة الطلب إلى `canceled_before_timed_out_by_user`

3. **استرداد النقاط (إن وجدت):**
   - إذا كان الدفع بالنقاط، يتم استرداد النقاط للمستخدم

## ملاحظات مهمة

- يتم حفظ السجلات تلقائياً عند تشغيل `php artisan orders:cancel-expired`
- السائق يمكنه الإلغاء فقط ضمن الوقت المحدد في `delivery_deadline_minutes`
- لا يمكن إلغاء الطلبات المخصصة بالفعل (التي لها order_id) من قبل السائق
- صاحب الطلب يمكنه إلغاء طلبه حتى لو كان مخصص لسائق، بشرط أن يكون في حالة `pending`
- جميع العمليات مسجلة في logs للمراجعة
