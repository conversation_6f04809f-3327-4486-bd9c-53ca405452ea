# فلترة المحطات بناءً على الموقع الجغرافي

## نظرة عامة

تم إضافة إمكانية فلترة المحطات بناءً على الموقع الجغرافي (اللات واللونج) بحيث يتم ترتيب النتائج من الأقرب إلى الأبعد.

## كيفية الاستخدام

### الطلب الأساسي (بدون فلترة)

```http
GET /api/stations
```

**الاستجابة:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "محطة الرياض",
      "latitude": 24.7136,
      "longitude": 46.6753
    },
    {
      "id": 2,
      "name": "محطة جدة",
      "latitude": 21.3891,
      "longitude": 39.8579
    }
  ]
}
```

### الطلب مع فلترة الموقع

```http
GET /api/stations?latitude=24.7000&longitude=46.7000
```

**المعاملات:**
- `latitude` (اختياري): خط العرض (بين -90 و 90)
- `longitude` (اختياري): خط الطول (بين -180 و 180)
- `per_page` (اختياري): عدد النتائج في الصفحة (افتراضي: 15)

**الاستجابة:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "محطة الرياض",
      "latitude": 24.7136,
      "longitude": 46.6753,
      "distance": 2.45
    },
    {
      "id": 3,
      "name": "محطة الدمام",
      "latitude": 26.3927,
      "longitude": 50.0778,
      "distance": 387.21
    },
    {
      "id": 2,
      "name": "محطة جدة",
      "latitude": 21.3891,
      "longitude": 39.8579,
      "distance": 879.33
    }
  ]
}
```

## ملاحظات مهمة

1. **المعاملات المطلوبة:** إذا تم تمرير أحد المعاملين (latitude أو longitude) فيجب تمرير الآخر أيضاً
2. **وحدة المسافة:** المسافة محسوبة بالكيلومتر
3. **دقة المسافة:** المسافة مقربة لرقمين عشريين
4. **الترتيب:** النتائج مرتبة من الأقرب إلى الأبعد
5. **حساب المسافة:** يتم استخدام صيغة Haversine لحساب المسافة بدقة

## رسائل الخطأ

### معاملات ناقصة
```http
GET /api/stations?latitude=24.7000
```

**الاستجابة:**
```json
{
  "message": "Both latitude and longitude are required when filtering by location"
}
```

### قيم غير صحيحة
```http
GET /api/stations?latitude=invalid&longitude=46.7000
```

**الاستجابة:**
```json
{
  "message": "Latitude and longitude must be numeric values"
}
```

### قيم خارج النطاق
```http
GET /api/stations?latitude=100&longitude=46.7000
```

**الاستجابة:**
```json
{
  "message": "Latitude must be between -90 and 90"
}
```

```http
GET /api/stations?latitude=24.7000&longitude=200
```

**الاستجابة:**
```json
{
  "message": "Longitude must be between -180 and 180"
}
```

## أمثلة عملية

### البحث عن المحطات القريبة من الرياض
```http
GET /api/stations?latitude=24.7136&longitude=46.6753&per_page=5
```

### البحث عن المحطات القريبة من جدة
```http
GET /api/stations?latitude=21.3891&longitude=39.8579&per_page=10
```

### البحث عن المحطات القريبة من الدمام
```http
GET /api/stations?latitude=26.3927&longitude=50.0778
```
