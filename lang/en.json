{"Navigation": "Navigation", "Dashboard": "Dashboard", "confirm_delete": "Confirm Delete", "are_you_sure_delete": "Are you sure you want to delete?", "action_cannot_undone": "This action cannot be undone", "cancel": "Cancel", "delete": "Delete", "success": "Success!", "user_created_successfully": "User created successfully", "user_updated_successfully": "User updated successfully", "user_deleted_successfully": "User deleted successfully", "yes": "Yes", "no": "No", "users": "Users", "home": "Home", "add_user": "Add User", "users_list": "Users List", "Export": "Export", "Print": "Print", "name": "Name", "email": "Email", "Role": "Role", "created_at": "Created At", "actions": "Actions", "No Role": "No Role", "no_data": "No Data", "languages": "Languages", "add_language": "Add Language", "languages_list": "Languages List", "code": "Code", "flag": "Flag", "direction": "Direction", "status": "Status", "active": "Active", "inactive": "Inactive", "enter_language_name": "Enter language name", "save": "Save", "edit_language": "Edit Language", "update": "Update", "view_language": "View Language", "close": "Close", "language_created_successfully": "Language created successfully", "language_updated_successfully": "Language updated successfully", "language_deleted_successfully": "Language deleted successfully", "roles": "Roles", "add_role": "Add Role", "roles_list": "Roles List", "guard_name": "Guard Name", "users_count": "Users Count", "enter_role_name": "Enter role name", "edit_role": "Edit Role", "view_role": "View Role", "role_created_successfully": "Role created successfully", "role_updated_successfully": "Role updated successfully", "role_deleted_successfully": "Role deleted successfully", "permissions": "Permissions", "no_permissions": "No permissions assigned", "users.view": "View Users", "users.create": "Create User", "users.edit": "Edit Users", "users.delete": "Delete Users", "roles.view": "View Roles", "roles.create": "Create Role", "roles.edit": "Edit Roles", "roles.delete": "Delete Roles", "languages.view": "View Languages", "languages.create": "Create Language", "languages.edit": "Edit Languages", "languages.delete": "Delete Languages", "pages.view": "View Pages", "pages.create": "Create Page", "pages.edit": "Edit Pages", "pages.delete": "Delete Pages", "orders.view": "View Orders", "orders.create": "Create Order", "orders.edit": "Edit Orders", "orders.delete": "Delete Orders", "dashboard.view": "View Dashboard", "settings.view": "View Settings", "settings.edit": "Edit Settings", "dashboard": "Dashboard", "settings": "Settings", "default": "<PERSON><PERSON><PERSON>", "set_default": "<PERSON>", "default_language_updated": "Default language updated successfully", "verification_code_sent": "Verification code sent to your phone", "registration_successful": "Registration successful", "login_successful": "Login successful", "verification_code_resent": "Verification code resent", "logout_successful": "Logout successful", "invalid_verification_code": "Invalid verification code", "verification_code_expired": "Verification code expired", "invalid_credentials": "Invalid credentials", "registration_not_found": "Registration not found", "phone_already_registered": "Phone number already registered. You can login directly.", "validation_error": "Validation error", "registration_error": "Registration error occurred", "verification_error": "Verification error occurred", "login_error": "Login error occurred", "resend_error": "Resend error occurred", "logout_error": "Logout error occurred", "verification_code_error": "Error verifying the code", "site_settings": "Site Settings", "general_settings": "General Settings", "contact_settings": "Contact Settings", "social_settings": "Social Media", "mail_settings": "Mail Settings", "seo_settings": "SEO Settings", "security_settings": "Security Settings", "payment_settings": "Payment Settings", "storage_settings": "Storage Settings", "settings_updated_successfully": "Settings updated successfully", "site_name": "Site Name", "site_description": "Site Description", "site_keywords": "Site Keywords", "site_logo": "Site Logo", "site_favicon": "Site Favicon", "maintenance_mode": "Maintenance Mode", "contact_email": "Contact Email", "contact_phone": "Contact Phone", "contact_address": "Contact Address", "contact_whatsapp": "WhatsApp", "enable": "Enable", "current_file": "Current File", "national_id": "National ID", "stations": "Stations", "types": "Types", "latitude": "Latitude", "longitude": "Longitude", "station_created_successfully": "Station created successfully", "station_updated_successfully": "Station updated successfully", "station_deleted_successfully": "Station deleted successfully", "type_created_successfully": "Type created successfully", "type_updated_successfully": "Type updated successfully", "type_deleted_successfully": "Type deleted successfully", "add_station": "Add Station", "edit_station": "Edit Station", "delete_station": "Delete Station", "stations_list": "Stations List", "are_you_sure_delete_station": "Are you sure you want to delete station", "add_type": "Add Type", "edit_type": "Edit Type", "delete_type": "Delete Type", "types_list": "Types List", "are_you_sure_delete_type": "Are you sure you want to delete type", "unauthorized": "Unauthorized", "show_deleted": "Show Deleted", "restore": "Rest<PERSON>", "permanent_delete": "Permanent Delete", "station_restored_successfully": "Station restored successfully", "station_permanently_deleted": "Station permanently deleted", "type_restored_successfully": "Type restored successfully", "type_permanently_deleted": "Type permanently deleted", "restore_station": "Restore Station", "restore_type": "Restore Type", "permanent_delete_station": "Permanently Delete Station", "permanent_delete_type": "Permanently Delete Type", "this_action_cannot_be_undone": "This action cannot be undone", "pricings": "Pricings", "add_pricing": "Add Pricing", "edit_pricing": "Edit Pricing", "delete_pricing": "Delete Pricing", "restore_pricing": "Restore Pricing", "permanent_delete_pricing": "Permanently Delete Pricing", "from_station": "From Station", "to_station": "To Station", "select_station": "Select Station", "select_type": "Select Type", "price": "Price", "are_you_sure_delete_pricing": "Are you sure you want to delete this pricing", "pricing_created_successfully": "Pricing created successfully", "pricing_updated_successfully": "Pricing updated successfully", "pricing_deleted_successfully": "Pricing deleted successfully", "pricing_restored_successfully": "Pricing restored successfully", "pricing_permanently_deleted": "Pricing permanently deleted", "pricing_will_be_created_for_all_types": "Pricing will be created for all types with the same price", "prices_by_type": "Prices by Type", "type": "Type", "types_count": "Types Count", "route_already_exists": "This route already exists", "are_you_sure_delete_route_pricing": "Are you sure you want to delete all prices for this route?", "view_pricing": "View Pricing", "view": "View", "from_station_required": "Please select departure station", "to_station_required": "Please select arrival station", "stations_must_be_different": "Departure station must be different from arrival station", "price_must_be_number": "Price must be a number", "price_must_be_positive": "Price must be greater than or equal to zero", "restore_route_pricing": "Are you sure you want to restore all prices for this route?", "permanent_delete_route_pricing": "Are you sure you want to permanently delete all prices for this route?", "no_data_found": "No data found", "no_pricing_available_for_selected_route": "No pricing available for the selected route", "price_does_not_match_pricing_table": "The entered price does not match the pricing table", "wallet": "Wallet", "current_balance": "Current Balance", "total_earned": "Total Earned", "total_spent": "Total Spent", "point": "Point", "transactions": "Transactions", "transaction_type": "Transaction Type", "all": "All", "earned": "Earned", "spent": "Spent", "from_date": "From Date", "to_date": "To Date", "search": "Search", "date": "Date", "description": "Description", "amount": "Amount", "balance": "Balance", "no_transactions_found": "No transactions found", "status_must_be_different_from_current": "Status must be different from current status", "cannot_assign_own_orders": "Cannot assign your own orders", "order_already_has_driver": "Order already has a driver assigned", "no_available_orders": "No available orders for delivery", "no_available_orders_for_type": "No available orders for this type and stations", "pending": "Pending", "canceled_before_timed_out_by_user": "Canceled by user before timeout", "canceled_before_timed_out_by_admin": "Canceled by admin before timeout", "canceled_timed_out_by_system": "Canceled automatically after timeout", "confirmed": "Confirmed", "canceled_after_confirmed_by_admin": "Canceled by admin after confirmation", "picked_up": "Picked up", "canceled_after_picked_up_by_admin": "Canceled by admin after pickup", "delivered": "Delivered", "canceled_after_delivered_by_admin": "Canceled by admin after delivery", "pending_interest_exists": "You have a pending interest that is not completed yet", "waiting_for_order": "Waiting for you to arrive at the departure station", "order_status.owner.pending": "Delivery of the starting station", "order_status.owner.canceled_before_timed_out_by_user": "Order canceled by user before timeout", "order_status.owner.canceled_before_timed_out_by_admin": "Order canceled by admin before timeout", "order_status.owner.canceled_timed_out_by_system": "Order automatically canceled after timeout", "order_status.owner.confirmed": "Order confirmed", "order_status.owner.canceled_after_confirmed_by_admin": "Order canceled by admin after confirmation", "order_status.owner.picked_up": "Order picked up", "order_status.owner.canceled_after_picked_up_by_admin": "Order canceled by admin after pickup", "order_status.owner.delivered": "Order delivered successfully", "order_status.owner.canceled_after_delivered_by_admin": "Order canceled by admin after delivery", "order_status.other.pending": "New order needs review", "order_status.other.canceled_before_timed_out_by_user": "Order canceled by user", "order_status.other.canceled_before_timed_out_by_admin": "Order canceled by admin", "order_status.other.canceled_timed_out_by_system": "Order automatically canceled", "order_status.other.confirmed": "Order confirmed", "order_status.other.canceled_after_confirmed_by_admin": "Order canceled by admin", "order_status.other.picked_up": "Order picked up", "order_status.other.canceled_after_picked_up_by_admin": "Order canceled by admin", "order_status.other.delivered": "Order delivered to customer", "order_status.other.canceled_after_delivered_by_admin": "Order canceled by admin", "order_status.owner.canceled_by_user": "Order canceled by user", "order_status.owner.canceled_by_admin": "Order canceled by admin", "profile_updated_successfully": "Profile updated successfully", "profile_update_error": "Failed to update profile. Please try again.", "password_reset_link_sent": "Password reset link has been sent to your phone number.", "password_reset_successful": "Password has been reset successfully", "password_reset_error": "Failed to reset password. Please try again.", "password_reset_link_error": "Failed to send password reset link. Please try again.", "invalid_reset_token": "Invalid or expired reset token.", "password_updated": "Password has been updated successfully.", "verification_code_valid": "Verification code is valid", "verification_code_required": "Verification code is required", "verification_code_size": "Verification code must be 6 digits", "reset_code_sent": "Password reset code has been sent to your phone", "invalid_or_expired_code": "Invalid or expired verification code", "failed_to_send_reset_code": "Failed to send reset code", "auth.reset_code_message": "Your password reset code is: :code. Valid for 10 minutes.", "user_not_found": "No user found with this phone number", "total_balance": "Total Balance", "total_users": "Total Users", "all_users": "All Users", "user": "User", "search_user_description": "Search in user or description", "search_user_description_barcode": "Search in user, description or barcode", "search_name_email_phone": "Search in name, email or phone", "all_roles": "All Roles", "per_page": "Per Page", "reset_filters": "Reset Filters", "phone": "Phone", "buy_cards": "Buy Cards", "buy_card": "Buy Card", "user_statistics": "User Statistics", "current_points_balance": "Current Points Balance", "delivered_orders": "Delivered Orders", "current_deliveries": "Current Deliveries", "created_orders": "Created Orders", "conversion_rate_info": "Conversion Rate", "currency_unit": "ils", "points": "Points", "card_name": "Card Name", "enter_card_name": "Enter Card Name", "cash_amount": "Cash Amount", "points_amount": "Points Amount", "insufficient_points_warning": "Insufficient Points", "required": "Required", "available": "Available", "card_purchase": "Card Purchase", "card_name_required": "Card name is required", "points_amount_required": "Points amount is required", "points_amount_min": "Points amount must be greater than zero", "card_purchased_successfully": "Card purchased successfully", "insufficient_points": "Insufficient points", "pending_points": "Pending Points", "total_points": "Total Points", "insufficient_points_for_order": "Insufficient points for order (required: :required, available: :available)", "order_payment": "Order Payment", "failed_to_add_points": "Failed to add points", "invalid_points_rate": "Invalid points rate", "order_payment_description": "Order payment #:barcode", "delivery_points_earned": "Delivery points for order #:barcode", "transactions_retrieved_successfully": "Transactions retrieved successfully", "transaction_retrieved_successfully": "Transaction retrieved successfully", "transaction_not_found": "Transaction not found", "statistics_retrieved_successfully": "Statistics retrieved successfully", "something_went_wrong": "Something went wrong", "pages": "Pages", "page": "Page", "add_page": "Add Page", "edit_page": "Edit Page", "page_created_successfully": "Page created successfully", "page_updated_successfully": "Page updated successfully", "page_deleted_successfully": "Page deleted successfully", "page_restored_successfully": "<PERSON> restored successfully", "page_archived_successfully": "Page archived successfully", "page_duplicated_successfully": "Page duplicated successfully", "title": "Title", "slug": "Slug", "content": "Content", "excerpt": "Excerpt", "meta_title": "Meta Title", "meta_description": "Meta Description", "meta_keywords": "Meta Keywords", "featured_image": "Featured Image", "is_featured": "Featured", "sort_order": "Sort Order", "published_at": "Published At", "draft": "Draft", "published": "Published", "archived": "Archived", "featured": "Featured", "not_featured": "Not Featured", "publish_settings": "Publish Settings", "page_info": "Page Information", "created_by": "Created by", "updated_by": "Updated By", "view_page": "View Page", "duplicate": "Duplicate", "archive": "Archive", "preview": "Preview", "preview_feature_coming_soon": "Preview feature coming soon", "all_pages": "All Pages", "search_pages": "Search Pages", "filter_by_status": "Filter by Status", "hide_deleted": "Hide Deleted", "no_pages_found": "No pages found", "page_content": "Page Content", "enter_page_title": "Enter page title", "enter_page_content": "Enter page content", "enter_page_excerpt": "Enter page excerpt", "enter_slug": "Enter slug", "enter_meta_title": "Enter meta title", "enter_meta_description": "Enter meta description", "enter_meta_keywords": "Enter meta keywords", "select_status": "Select Status", "select_featured_image": "Select featured image", "publish_date": "Publish Date", "auto_generate": "Auto Generate", "generate_slug": "Generate Slug", "page_settings": "Page Settings", "basic_info": "Basic Information", "advanced_settings": "Advanced Settings", "image_url": "Image URL", "current_url": "Current URL", "page_information": "Page Information", "last_updated": "Last Updated", "page_actions": "Page Actions", "quick_actions": "Quick Actions", "save_as_draft": "Save as Draft", "save_and_publish": "Save and Publish", "back_to_pages": "Back to Pages", "page_url_preview": "Page URL Preview", "slug_help": "URL slug will be auto-generated from title if left empty", "meta_keywords_help": "Type keywords and press Enter", "featured_help": "Featured pages appear prominently", "sort_order_help": "Display order (lower numbers appear first)", "publish_date_help": "Leave empty for immediate publishing", "excerpt_help": "Short summary that appears in search results", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "about_us": "About Us", "contact_us": "Contact Us", "faq": "FAQ", "help": "Help", "support": "Support", "refund_policy": "Refund Policy", "shipping_policy": "Shipping Policy", "cookie_policy": "<PERSON><PERSON>", "disclaimer": "Disclaimer", "careers": "Careers", "press": "Press", "blog": "Blog", "news": "News", "footer_description": "We provide the best services and innovative technical solutions for our clients.", "quick_links": "Quick Links", "legal": "Legal", "contact_info": "Contact Info", "riyadh_saudi_arabia": "Riyadh, Saudi Arabia", "all_rights_reserved": "All rights reserved", "made_with": "Made with", "in_saudi_arabia": "in Saudi Arabia", "published_on": "Published on", "page_not_found_or_disabled": "Page not found or disabled", "page_not_found": "Page not found", "orders": "Orders", "order": "Order", "total_orders": "Total Orders", "orders_list": "Orders List", "no_orders_found": "No Orders Found", "no_orders_match_criteria": "No orders match the search criteria", "search_orders": "Search Orders", "filters": "Filters", "clear": "Clear", "refresh": "Refresh", "barcode": "Barcode", "route": "Route", "receiver": "Receiver", "order_created": "Order Created", "order_confirmed": "Order Confirmed", "order_picked_up": "Order Picked Up", "order_delivered": "Order Delivered", "order_status_updated_successfully": "Order status updated successfully", "order_deleted_successfully": "Order deleted successfully", "order_refreshed": "Order refreshed", "invalid_status_transition": "Invalid status transition", "print_feature_coming_soon": "Print feature coming soon", "export_feature_coming_soon": "Export feature coming soon", "bulk_action_completed": "Bulk action completed", "statistics_updated": "Statistics updated", "unknown": "Unknown", "order_details": "Order Details", "order_progress": "Order Progress", "driver_info": "Driver Info", "tracking_history": "Tracking History", "no_tracking_history": "No tracking history", "update_status": "Update Status", "update_order_status": "Update Order Status", "current_status": "Current Status", "new_status": "New Status", "optional": "Optional", "add_note_for_status_change": "Add note for status change", "are_you_sure_update_status": "Are you sure you want to update the status?", "assigned_at": "Assigned at", "by": "By", "system": "System", "points_used": "Points Used", "edit": "Edit", "print": "Print", "confirm_status_change": "Confirm Status Change", "are_you_sure_change_status_to": "Are you sure you want to change status to", "yes_change": "Yes, Change Status", "yes_delete": "Yes, Delete", "order_not_found": "Order not found", "all_orders": "All Orders", "stuck_orders": "Stuck Orders", "total_revenue": "Total Revenue", "today_orders": "Today Orders", "total_stations": "Total Stations", "delivery_rate": "Delivery Rate", "today_revenue": "Today Revenue", "recent_orders": "Recent Orders", "view_all": "View All", "no_recent_orders": "No recent orders", "no_stuck_orders": "No stuck orders", "error": "Error!", "Cannot modify super admin role": "Cannot modify super admin role", "Cannot delete admin or super admin user": "Cannot delete admin or super admin user", "Super Admin role cannot be changed": "Super Admin role cannot be changed", "Cannot delete admin users": "Cannot delete admin users", "ok": "OK", "Select Role": "Select Role"}