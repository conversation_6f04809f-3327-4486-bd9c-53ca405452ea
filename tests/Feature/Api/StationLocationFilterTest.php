<?php

namespace Tests\Feature\Api;

use App\Models\Station;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class StationLocationFilterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // إنشاء مستخدم للاختبار
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /** @test */
    public function itReturnsStationsWithoutLocationFilter()
    {
        // إنشاء محطات للاختبار
        Station::factory()->create(['name' => 'Station 1', 'latitude' => 24.7136, 'longitude' => 46.6753]);
        Station::factory()->create(['name' => 'Station 2', 'latitude' => 21.3891, 'longitude' => 39.8579]);
        Station::factory()->create(['name' => 'Station 3', 'latitude' => 26.3927, 'longitude' => 50.0778]);

        $response = $this->getJson('/api/stations');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude',
                        ],
                    ],
                ]);

        // التأكد من عدم وجود حقل المسافة
        $response->assertJsonMissing(['distance']);
    }

    /** @test */
    public function itReturnsStationsSortedByDistanceWhenLocationProvided()
    {
        // إنشاء محطات في مواقع مختلفة
        $station1 = Station::factory()->create([
            'name' => 'Riyadh Station',
            'latitude' => 24.7136,
            'longitude' => 46.6753,
        ]);

        $station2 = Station::factory()->create([
            'name' => 'Jeddah Station',
            'latitude' => 21.3891,
            'longitude' => 39.8579,
        ]);

        $station3 = Station::factory()->create([
            'name' => 'Dammam Station',
            'latitude' => 26.3927,
            'longitude' => 50.0778,
        ]);

        // الاختبار من موقع قريب من الرياض
        $response = $this->getJson('/api/stations?latitude=24.7000&longitude=46.7000');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude',
                            'distance',
                        ],
                    ],
                ]);

        $stations = $response->json('data');

        // التأكد من أن المحطات مرتبة حسب المسافة (الأقرب أولاً)
        $this->assertTrue($stations[0]['distance'] <= $stations[1]['distance']);
        $this->assertTrue($stations[1]['distance'] <= $stations[2]['distance']);
    }

    /** @test */
    public function itValidatesLatitudeAndLongitudeParameters()
    {
        // اختبار تمرير latitude فقط
        $response = $this->getJson('/api/stations?latitude=24.7000');
        $response->assertStatus(422)
                ->assertJson([
                    'message' => 'Both latitude and longitude are required when filtering by location',
                ]);

        // اختبار تمرير longitude فقط
        $response = $this->getJson('/api/stations?longitude=46.7000');
        $response->assertStatus(422)
                ->assertJson([
                    'message' => 'Both latitude and longitude are required when filtering by location',
                ]);

        // اختبار قيم غير رقمية
        $response = $this->getJson('/api/stations?latitude=invalid&longitude=46.7000');
        $response->assertStatus(422)
                ->assertJson([
                    'message' => 'Latitude and longitude must be numeric values',
                ]);

        // اختبار latitude خارج النطاق
        $response = $this->getJson('/api/stations?latitude=100&longitude=46.7000');
        $response->assertStatus(422)
                ->assertJson([
                    'message' => 'Latitude must be between -90 and 90',
                ]);

        // اختبار longitude خارج النطاق
        $response = $this->getJson('/api/stations?latitude=24.7000&longitude=200');
        $response->assertStatus(422)
                ->assertJson([
                    'message' => 'Longitude must be between -180 and 180',
                ]);
    }

    /** @test */
    public function itReturnsValidDistanceCalculations()
    {
        // إنشاء محطة في الرياض
        $station = Station::factory()->create([
            'name' => 'Riyadh Station',
            'latitude' => 24.7136,
            'longitude' => 46.6753,
        ]);

        // الاختبار من نفس الموقع تقريباً
        $response = $this->getJson('/api/stations?latitude=24.7136&longitude=46.6753');

        $response->assertStatus(200);
        $stations = $response->json('data');

        // التأكد من أن المسافة قريبة جداً من الصفر
        $this->assertLessThan(1, $stations[0]['distance']); // أقل من كيلومتر واحد
    }
}
