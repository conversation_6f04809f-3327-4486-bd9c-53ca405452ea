<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderDriver;
use App\Models\OrderDriverHistory;
use App\Models\Setting;
use App\Models\Station;
use App\Models\Type;
use App\Models\User;
use App\Services\OrderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class OrderDriverHistoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $orderService;
    protected $driver;
    protected $station1;
    protected $station2;
    protected $type;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->orderService = new OrderService();
        
        // Create test data
        $this->driver = User::factory()->create(['role' => 'driver']);
        $this->station1 = Station::factory()->create();
        $this->station2 = Station::factory()->create();
        $this->type = Type::factory()->create();
        
        // Set delivery deadline setting
        Setting::create([
            'key' => 'delivery_deadline_minutes',
            'value' => '30'
        ]);
    }

    /** @test */
    public function it_saves_expired_driver_interest_to_history_before_deletion()
    {
        // Create an expired driver interest
        $orderDriver = OrderDriver::create([
            'driver_id' => $this->driver->id,
            'type_id' => $this->type->id,
            'from_station_id' => $this->station1->id,
            'to_station_id' => $this->station2->id,
            'assigned_at' => now()->subMinutes(35), // Expired
            'created_at' => now()->subMinutes(35),
            'updated_at' => now()->subMinutes(35),
        ]);

        // Run the cleanup method
        $deletedCount = $this->orderService->cleanExpiredDriverInterests();

        // Assert that one record was deleted
        $this->assertEquals(1, $deletedCount);

        // Assert that the original record is deleted
        $this->assertDatabaseMissing('order_drivers', ['id' => $orderDriver->id]);

        // Assert that the record is saved in history
        $this->assertDatabaseHas('order_drivers_history', [
            'original_order_driver_id' => $orderDriver->id,
            'driver_id' => $this->driver->id,
            'type_id' => $this->type->id,
            'from_station_id' => $this->station1->id,
            'to_station_id' => $this->station2->id,
            'cancellation_reason' => 'expired_by_system',
        ]);
    }

    /** @test */
    public function it_allows_driver_to_cancel_interest_within_deadline()
    {
        // Create a fresh driver interest (within deadline)
        $orderDriver = OrderDriver::create([
            'driver_id' => $this->driver->id,
            'type_id' => $this->type->id,
            'from_station_id' => $this->station1->id,
            'to_station_id' => $this->station2->id,
            'assigned_at' => now(),
        ]);

        // Cancel the interest
        $result = $this->orderService->cancelDriverInterest(
            $orderDriver->id,
            $this->driver->id,
            'cancelled_by_driver',
            'Test cancellation'
        );

        // Assert cancellation was successful
        $this->assertTrue($result);

        // Assert that the original record is deleted
        $this->assertDatabaseMissing('order_drivers', ['id' => $orderDriver->id]);

        // Assert that the record is saved in history
        $this->assertDatabaseHas('order_drivers_history', [
            'original_order_driver_id' => $orderDriver->id,
            'driver_id' => $this->driver->id,
            'cancellation_reason' => 'cancelled_by_driver',
            'cancellation_note' => 'Test cancellation',
        ]);
    }

    /** @test */
    public function it_prevents_driver_from_canceling_expired_interest()
    {
        // Create an expired driver interest
        $orderDriver = OrderDriver::create([
            'driver_id' => $this->driver->id,
            'type_id' => $this->type->id,
            'from_station_id' => $this->station1->id,
            'to_station_id' => $this->station2->id,
            'assigned_at' => now()->subMinutes(35),
            'created_at' => now()->subMinutes(35),
        ]);

        // Attempt to cancel the expired interest
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('لا يمكن إلغاء الاهتمام بعد انتهاء الوقت المسموح');

        $this->orderService->cancelDriverInterest(
            $orderDriver->id,
            $this->driver->id,
            'cancelled_by_driver'
        );
    }

    /** @test */
    public function it_prevents_driver_from_canceling_assigned_order()
    {
        $order = Order::factory()->create();
        
        // Create a driver interest with assigned order
        $orderDriver = OrderDriver::create([
            'order_id' => $order->id,
            'driver_id' => $this->driver->id,
            'type_id' => $this->type->id,
            'from_station_id' => $this->station1->id,
            'to_station_id' => $this->station2->id,
            'assigned_at' => now(),
        ]);

        // Attempt to cancel the assigned order
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('اهتمام السائق غير موجود أو لا يمكن إلغاؤه');

        $this->orderService->cancelDriverInterest(
            $orderDriver->id,
            $this->driver->id,
            'cancelled_by_driver'
        );
    }
}
