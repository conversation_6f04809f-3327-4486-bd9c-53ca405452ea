<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PageController;
use App\Http\Controllers\Api\StationController;
use App\Http\Controllers\Api\TransactionController;
use App\Http\Controllers\Api\TypeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// مسار التوثيق
Route::get('/docs/pages', [App\Http\Controllers\Api\ApiDocumentationController::class, 'pages'])->name('api.docs.pages');

// مسارات المصادقة
Route::prefix('auth')->group(function () {
    // Public routes
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/verify-code', [AuthController::class, 'verifyCode']);
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/resend-code', [AuthController::class, 'resendCode']);
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/verify-reset-code', [AuthController::class, 'verifyResetCode']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
        Route::post('/profile', [AuthController::class, 'updateProfile']);
        Route::delete('/profile/photo', [AuthController::class, 'deleteProfilePhoto']);
    });
});

// مسارات الصفحات العامة (بدون مصادقة)
Route::prefix('pages')->group(function () {
    Route::get('/published', [PageController::class, 'published'])->name('api.pages.published');
    Route::get('/featured', [PageController::class, 'featured'])->name('api.pages.featured');
    Route::get('/search', [PageController::class, 'search'])->name('api.pages.search');
    Route::get('/slug/{slug}', [PageController::class, 'bySlug'])->name('api.pages.by-slug');
});

// مسارات محمية
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    Route::apiResource('stations', StationController::class)->only(['index', 'show']);
    Route::apiResource('types', TypeController::class)->only(['index', 'show']);

    // مسارات إدارة الصفحات (محمية)
    // Route::apiResource('pages', PageController::class)->names([
    //     'index' => 'api.pages.index',
    //     'store' => 'api.pages.store',
    //     'show' => 'api.pages.show',
    //     'update' => 'api.pages.update',
    //     'destroy' => 'api.pages.destroy',
    // ])->middleware('auth:sanctum');

    Route::get('/pricing', [App\Http\Controllers\Api\PricingController::class, 'getPrice']);

    // Orders API
    Route::get('/orders', [App\Http\Controllers\Api\OrderController::class, 'index']);
    Route::post('/orders', [App\Http\Controllers\Api\OrderController::class, 'store']);
    Route::get('/orders/history', [App\Http\Controllers\Api\OrderController::class, 'history']);
    Route::get('/orders/{id}', [App\Http\Controllers\Api\OrderController::class, 'show']);
    Route::post('/orders/status', [App\Http\Controllers\Api\OrderController::class, 'updateStatus']);
    Route::post('/orders/cancel-order', [App\Http\Controllers\Api\OrderController::class, 'cancelOrder']);

    // Delivery Search API
    Route::get('/delivery/search', [App\Http\Controllers\Api\DeliverySearchController::class, 'search']);
    Route::post('/delivery/interest', [App\Http\Controllers\Api\DeliverySearchController::class, 'registerInterest']);
    Route::post('/delivery/get-order', [App\Http\Controllers\Api\DeliverySearchController::class, 'getRandomOrder']);
    Route::post('/delivery/cancel-order-delivery', [App\Http\Controllers\Api\DeliverySearchController::class, 'cancelInterest']);

    // Attendance API
    Route::post('/add-attendance', [App\Http\Controllers\Api\AttendanceController::class, 'store']);
    Route::post('/delete-attendance', [App\Http\Controllers\Api\AttendanceController::class, 'destroy']);

    // Transactions API
    // Route::prefix('transactions')->group(function () {
    //     Route::get('/', [TransactionController::class, 'index']);
    //     Route::get('/earned', [TransactionController::class, 'earned']);
    //     Route::get('/spent', [TransactionController::class, 'spent']);
    //     Route::get('/statistics', [TransactionController::class, 'statistics']);
    //     Route::get('/{id}', [TransactionController::class, 'show']);
    // });
    Route::prefix('wallet')->group(function () {
        Route::get('/', 'App\Http\Controllers\Api\WalletController@getWallet');
        Route::get('/transactions', 'App\Http\Controllers\Api\WalletController@getTransactions');
        Route::get('/transactions/{transaction}', 'App\Http\Controllers\Api\WalletController@getTransactionDetails');
    });
});
