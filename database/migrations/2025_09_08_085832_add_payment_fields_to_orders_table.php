<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('payment_method', ['points', 'cash'])->default('cash')->after('to_station_id');
            // $table->integer('points')->default(0)->after('payment_method');
            $table->integer('points_used')->default(0)->after('points');
            $table->timestamp('delivery_deadline')->nullable()->after('points_used');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['payment_method',  'points_used', 'delivery_deadline']);
        });
    }
};
