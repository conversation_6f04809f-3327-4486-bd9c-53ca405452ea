<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('from_station_id')->nullable()->constrained('stations')->onDelete('set null');
            $table->foreignId('to_station_id')->nullable()->constrained('stations')->onDelete('set null');
            $table->foreignId('type_id')->nullable()->constrained('types')->onDelete('set null');
            $table->string('receiver_name')->nullable();
            $table->string('receiver_phone')->nullable();
            $table->text('note')->nullable();
            $table->decimal('price', 10, 2)->default(0);
            $table->enum('status', ['pending',
                'canceled_before_timed_out_by_user',
                'canceled_before_timed_out_by_admin',
                'canceled_timed_out_by_system',
                'confirmed',
                'canceled_after_confirmed_by_admin',
                'picked_up',
                'canceled_after_picked_up_by_admin',
                'delivered',
                'canceled_after_delivered_by_admin'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
