<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_drivers_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('original_order_driver_id')->nullable()->comment('ID of the original order_driver record');
            $table->foreignId('order_id')->nullable()->constrained('orders')->onDelete('set null');
            $table->foreignId('driver_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('type_id')->constrained('types')->onDelete('cascade');
            $table->foreignId('from_station_id')->constrained('stations')->onDelete('cascade');
            $table->foreignId('to_station_id')->constrained('stations')->onDelete('cascade');
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('notes')->nullable();
            $table->enum('cancellation_reason', [
                'expired_by_system',
                'cancelled_by_driver',
                'cancelled_by_admin',
            ])->comment('Reason for moving to history');
            $table->text('cancellation_note')->nullable()->comment('Additional note about cancellation');
            $table->timestamp('cancelled_at')->useCurrent()->comment('When the record was moved to history');
            $table->timestamp('original_created_at')->nullable()->comment('Original created_at from order_drivers');
            $table->timestamp('original_updated_at')->nullable()->comment('Original updated_at from order_drivers');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_drivers_history');
    }
};
