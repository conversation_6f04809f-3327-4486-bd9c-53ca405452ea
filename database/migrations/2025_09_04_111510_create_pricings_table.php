<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('pricings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_station_id')->constrained('stations')->onDelete('cascade');
            $table->foreignId('to_station_id')->constrained('stations')->onDelete('cascade');
            $table->foreignId('type_id')->constrained('types')->onDelete('cascade');
            $table->decimal('price', 10, 2)->default(0);
            $table->softDeletes();
            $table->timestamps();

            $table->unique(['from_station_id', 'to_station_id', 'type_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pricings');
    }
};
