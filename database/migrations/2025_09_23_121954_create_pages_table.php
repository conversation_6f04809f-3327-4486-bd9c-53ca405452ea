<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // العنوان - سيكون translatable
            $table->string('slug')->unique(); // الرابط المختصر
            $table->longText('content'); // المحتوى - سيكون translatable
            $table->text('excerpt')->nullable(); // مقتطف - سيكون translatable
            $table->string('meta_title')->nullable(); // عنوان SEO - سيكون translatable
            $table->text('meta_description')->nullable(); // وصف SEO - سيكون translatable
            $table->json('meta_keywords')->nullable(); // كلمات مفتاحية SEO - سيكون translatable
            $table->string('featured_image')->nullable(); // صورة مميزة
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft'); // حالة الصفحة
            $table->boolean('is_featured')->default(false); // صفحة مميزة
            $table->integer('sort_order')->default(0); // ترتيب الصفحة
            $table->timestamp('published_at')->nullable(); // تاريخ النشر
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null'); // منشئ الصفحة
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null'); // محدث الصفحة
            $table->softDeletes(); // حذف ناعم
            $table->timestamps();

            // فهارس
            $table->index(['status', 'published_at']);
            $table->index(['is_featured', 'sort_order']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
