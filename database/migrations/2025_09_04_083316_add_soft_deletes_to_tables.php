<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('stations', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('types', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('roles', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('languages', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('translations', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('stations', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('types', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('roles', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('languages', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('translations', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};