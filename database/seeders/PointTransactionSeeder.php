<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PointTransaction;
use App\Models\User;
use App\Models\UserPoint;

class PointTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();

        foreach ($users as $user) {
            // Create user points record if not exists
            $userPoints = UserPoint::firstOrCreate(
                ['user_id' => $user->id],
                ['current_balance' => 0]
            );

            $currentBalance = 0;

            // Create some sample transactions
            $transactions = [
                [
                    'type' => 'earned',
                    'amount' => 100,
                    'description' => 'مكافأة التسجيل',
                ],
                [
                    'type' => 'earned',
                    'amount' => 50,
                    'description' => 'مكافأة إكمال الطلب الأول',
                ],
                [
                    'type' => 'spent',
                    'amount' => 30,
                    'description' => 'خصم من تكلفة التوصيل',
                ],
                [
                    'type' => 'earned',
                    'amount' => 75,
                    'description' => 'مكافأة إحالة صديق',
                ],
                [
                    'type' => 'spent',
                    'amount' => 25,
                    'description' => 'استبدال نقاط',
                ],
            ];

            foreach ($transactions as $transactionData) {
                if ($transactionData['type'] === 'earned') {
                    $currentBalance += $transactionData['amount'];
                } else {
                    $currentBalance -= $transactionData['amount'];
                }

                PointTransaction::create([
                    'user_id' => $user->id,
                    'type' => $transactionData['type'],
                    'amount' => $transactionData['amount'],
                    'description' => $transactionData['description'],
                    'balance_after' => $currentBalance,
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);
            }

            // Update user points balance
            $userPoints->update(['current_balance' => $currentBalance]);
        }
    }
}
