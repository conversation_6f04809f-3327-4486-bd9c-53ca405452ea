<?php

namespace Database\Seeders;

use App\Models\Language;
use App\Models\Translation;
use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء اللغات
        $languages = [
            [
                'name' => 'English',
                'code' => 'en',
                'flag' => '🇺🇸',
                'direction' => 'ltr',
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'name' => 'العربية',
                'code' => 'ar',
                'flag' => '🇸🇦',
                'direction' => 'rtl',
                'is_active' => true,
                'is_default' => false,
            ],
            [
                'name' => 'עברית',
                'code' => 'he',
                'flag' => '🇮🇱',
                'direction' => 'rtl',
                'is_active' => true,
                'is_default' => false,
            ],
        ];

        foreach ($languages as $language) {
            Language::create($language);
        }

        // إنشاء ترجمات أساسية
        $translations = [
            'welcome' => [
                'en' => 'Welcome',
                'ar' => 'مرحباً',
                'he' => 'ברוך הבא',
            ],
            'dashboard' => [
                'en' => 'Dashboard',
                'ar' => 'لوحة التحكم',
                'he' => 'לוח בקרה',
            ],
            'users' => [
                'en' => 'Users',
                'ar' => 'المستخدمون',
                'he' => 'משתמשים',
            ],
            'languages' => [
                'en' => 'Languages',
                'ar' => 'اللغات',
                'he' => 'שפות',
            ],
            'settings' => [
                'en' => 'Settings',
                'ar' => 'الإعدادات',
                'he' => 'הגדרות',
            ],
        ];

        foreach ($translations as $key => $values) {
            foreach ($values as $languageCode => $value) {
                Translation::create([
                    'key' => $key,
                    'value' => $value,
                    'language_code' => $languageCode,
                ]);
            }
        }
    }
}