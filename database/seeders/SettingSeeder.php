<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            // إعدادات عامة
            ['key' => 'site_name', 'value' => 'DropX', 'type' => 'text', 'group' => 'general'],
            ['key' => 'site_description', 'value' => 'موقع DropX', 'type' => 'textarea', 'group' => 'general'],
            ['key' => 'site_keywords', 'value' => 'dropx, website', 'type' => 'text', 'group' => 'general'],
            ['key' => 'site_logo', 'value' => '', 'type' => 'file', 'group' => 'general'],
            ['key' => 'site_favicon', 'value' => '', 'type' => 'file', 'group' => 'general'],
            ['key' => 'maintenance_mode', 'value' => '0', 'type' => 'boolean', 'group' => 'general'],
            
            // معلومات الاتصال
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'email', 'group' => 'contact'],
            ['key' => 'contact_phone', 'value' => '+966500000000', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'contact_address', 'value' => 'الرياض، المملكة العربية السعودية', 'type' => 'textarea', 'group' => 'contact'],
            ['key' => 'contact_whatsapp', 'value' => '+966500000000', 'type' => 'text', 'group' => 'contact'],
            
            // وسائل التواصل الاجتماعي
            ['key' => 'social_facebook', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'social_twitter', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'social_instagram', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'social_linkedin', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'social_youtube', 'value' => '', 'type' => 'url', 'group' => 'social'],
            
            // إعدادات البريد الإلكتروني
            ['key' => 'mail_driver', 'value' => 'smtp', 'type' => 'select', 'group' => 'mail'],
            ['key' => 'mail_host', 'value' => 'smtp.gmail.com', 'type' => 'text', 'group' => 'mail'],
            ['key' => 'mail_port', 'value' => '587', 'type' => 'number', 'group' => 'mail'],
            ['key' => 'mail_username', 'value' => '', 'type' => 'text', 'group' => 'mail'],
            ['key' => 'mail_password', 'value' => '', 'type' => 'password', 'group' => 'mail'],
            ['key' => 'mail_encryption', 'value' => 'tls', 'type' => 'select', 'group' => 'mail'],
            ['key' => 'mail_from_address', 'value' => '<EMAIL>', 'type' => 'email', 'group' => 'mail'],
            ['key' => 'mail_from_name', 'value' => 'DropX', 'type' => 'text', 'group' => 'mail'],
            
            // إعدادات SEO
            ['key' => 'seo_title', 'value' => 'DropX - الموقع الرسمي', 'type' => 'text', 'group' => 'seo'],
            ['key' => 'seo_description', 'value' => 'موقع DropX الرسمي', 'type' => 'textarea', 'group' => 'seo'],
            ['key' => 'seo_keywords', 'value' => 'dropx, website, saudi', 'type' => 'text', 'group' => 'seo'],
            ['key' => 'google_analytics', 'value' => '', 'type' => 'textarea', 'group' => 'seo'],
            ['key' => 'google_tag_manager', 'value' => '', 'type' => 'text', 'group' => 'seo'],
            
            // إعدادات الأمان
            ['key' => 'recaptcha_site_key', 'value' => '', 'type' => 'text', 'group' => 'security'],
            ['key' => 'recaptcha_secret_key', 'value' => '', 'type' => 'password', 'group' => 'security'],
            ['key' => 'enable_registration', 'value' => '1', 'type' => 'boolean', 'group' => 'security'],
            ['key' => 'email_verification', 'value' => '1', 'type' => 'boolean', 'group' => 'security'],
            
            // إعدادات الدفع
            ['key' => 'currency', 'value' => 'SAR', 'type' => 'text', 'group' => 'payment'],
            ['key' => 'currency_symbol', 'value' => 'ر.س', 'type' => 'text', 'group' => 'payment'],
            ['key' => 'paypal_mode', 'value' => 'sandbox', 'type' => 'select', 'group' => 'payment'],
            ['key' => 'paypal_client_id', 'value' => '', 'type' => 'text', 'group' => 'payment'],
            ['key' => 'paypal_client_secret', 'value' => '', 'type' => 'password', 'group' => 'payment'],
            ['key' => 'stripe_public_key', 'value' => '', 'type' => 'text', 'group' => 'payment'],
            ['key' => 'stripe_secret_key', 'value' => '', 'type' => 'password', 'group' => 'payment'],
            
            // إعدادات التخزين
            ['key' => 'storage_driver', 'value' => 'local', 'type' => 'select', 'group' => 'storage'],
            ['key' => 'aws_access_key', 'value' => '', 'type' => 'text', 'group' => 'storage'],
            ['key' => 'aws_secret_key', 'value' => '', 'type' => 'password', 'group' => 'storage'],
            ['key' => 'aws_region', 'value' => 'us-east-1', 'type' => 'text', 'group' => 'storage'],
            ['key' => 'aws_bucket', 'value' => '', 'type' => 'text', 'group' => 'storage'],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}