<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المستخدمين
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',

            // صلاحيات الأدوار
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',

            // صلاحيات اللغات
            'languages.view',
            'languages.create',
            'languages.edit',
            'languages.delete',

            // صلاحيات الصفحات
            'pages.view',
            'pages.create',
            'pages.edit',
            'pages.delete',

            // صلاحيات الطلبات
            'orders.view',
            'orders.create',
            'orders.edit',
            'orders.delete',

            // صلاحيات النظام
            'dashboard.view',
            'settings.view',
            'settings.edit',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // إنشاء دور السوبر أدمن مع جميع الصلاحيات (لا يمكن تعديله أو حذفه)
        $superAdminRole = Role::firstOrCreate(['name' => 'super_admin']);
        $superAdminRole->syncPermissions($permissions);

        // إنشاء دور الأدمن مع جميع الصلاحيات
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions($permissions);

        // إنشاء دور المستخدم مع صلاحيات محدودة
        $userRole = Role::firstOrCreate(['name' => 'user']);
        $userRole->syncPermissions([
            'dashboard.view',
        ]);

        // إنشاء دور المحرر مع صلاحيات متوسطة
        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $editorRole->syncPermissions([
            'dashboard.view',
            'users.view',
            'languages.view',
            'languages.create',
            'languages.edit',
            'pages.view',
            'pages.create',
            'pages.edit',
            'orders.view',
            'orders.edit',
        ]);
    }
}
