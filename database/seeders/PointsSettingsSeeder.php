<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class PointsSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'cash_to_points_rate',
                'value' => '4',
                // 'description' => 'كم نقطة يحصل السائق لكل شيكل من سعر الطلب'
            ],
            [
                'key' => 'points_to_cash_rate',
                'value' => '5',
                // 'description' => 'كم شيكل تساوي النقطة الواحدة عند الاستخدام'
            ],
            [
                'key' => 'delivery_deadline_minutes',
                'value' => '30',
                // 'description' => 'كم دقيقة لدى المرسل لتسليم الطرد للمحطة (1440 = 24 ساعة)'
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
