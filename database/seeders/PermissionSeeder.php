<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',
            'permissions.view',
            'permissions.create',
            'permissions.edit',
            'permissions.delete',
            'languages.view',
            'languages.create',
            'languages.edit',
            'languages.delete',
            'translations.view',
            'translations.create',
            'translations.edit',
            'translations.delete',
            'stations.view',
            'stations.create',
            'stations.edit',
            'stations.delete',
            'types.view',
            'types.create',
            'types.edit',
            'types.delete',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }
    }
}
