<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء مستخدم مدير
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'national_id' => '1234567890',
            'phone' => '123',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        $admin->assignRole('admin');

        // إنشاء مستخدم عادي
        $user = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'national_id' => '0987654321',
            'phone' => '1234567890',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        $user->assignRole('user');
    }
}
