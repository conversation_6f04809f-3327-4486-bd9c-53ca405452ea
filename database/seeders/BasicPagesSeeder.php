<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;
use App\Models\User;

class BasicPagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user
        $adminUser = User::whereHas('roles', function($query) {
            $query->where('name', 'admin');
        })->first();

        if (!$adminUser) {
            $adminUser = User::first();
        }

        if (!$adminUser) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        $basicPages = [
            [
                'title' => [
                    'ar' => 'سياسة الخصوصية',
                    'en' => 'Privacy Policy'
                ],
                'slug' => 'privacy-policy',
                'content' => [
                    'ar' => $this->getPrivacyPolicyContentAr(),
                    'en' => $this->getPrivacyPolicyContentEn()
                ],
                'excerpt' => [
                    'ar' => 'تعرف على كيفية جمع واستخدام وحماية بياناتك الشخصية',
                    'en' => 'Learn how we collect, use, and protect your personal data'
                ],
                'meta_title' => [
                    'ar' => 'سياسة الخصوصية - حماية بياناتك',
                    'en' => 'Privacy Policy - Protecting Your Data'
                ],
                'meta_description' => [
                    'ar' => 'اطلع على سياسة الخصوصية الخاصة بنا لفهم كيفية التعامل مع بياناتك الشخصية وحمايتها',
                    'en' => 'Read our privacy policy to understand how we handle and protect your personal information'
                ],
                'status' => 'published',
                'sort_order' => 1
            ],
            [
                'title' => [
                    'ar' => 'شروط الاستخدام',
                    'en' => 'Terms of Service'
                ],
                'slug' => 'terms-of-service',
                'content' => [
                    'ar' => $this->getTermsOfServiceContentAr(),
                    'en' => $this->getTermsOfServiceContentEn()
                ],
                'excerpt' => [
                    'ar' => 'الشروط والأحكام التي تحكم استخدام خدماتنا',
                    'en' => 'Terms and conditions governing the use of our services'
                ],
                'meta_title' => [
                    'ar' => 'شروط الاستخدام - القواعد والأحكام',
                    'en' => 'Terms of Service - Rules and Conditions'
                ],
                'meta_description' => [
                    'ar' => 'اقرأ شروط الاستخدام لفهم القواعد والأحكام التي تحكم استخدام خدماتنا',
                    'en' => 'Read our terms of service to understand the rules and conditions governing our services'
                ],
                'status' => 'published',
                'sort_order' => 2
            ],
            [
                'title' => [
                    'ar' => 'من نحن',
                    'en' => 'About Us'
                ],
                'slug' => 'about-us',
                'content' => [
                    'ar' => $this->getAboutUsContentAr(),
                    'en' => $this->getAboutUsContentEn()
                ],
                'excerpt' => [
                    'ar' => 'تعرف على قصتنا ورؤيتنا ورسالتنا',
                    'en' => 'Learn about our story, vision, and mission'
                ],
                'meta_title' => [
                    'ar' => 'من نحن - قصتنا ورؤيتنا',
                    'en' => 'About Us - Our Story and Vision'
                ],
                'meta_description' => [
                    'ar' => 'اكتشف المزيد عن شركتنا وقيمنا ورؤيتنا للمستقبل',
                    'en' => 'Discover more about our company, values, and vision for the future'
                ],
                'status' => 'published',
                'sort_order' => 3
            ],
            [
                'title' => [
                    'ar' => 'اتصل بنا',
                    'en' => 'Contact Us'
                ],
                'slug' => 'contact-us',
                'content' => [
                    'ar' => $this->getContactUsContentAr(),
                    'en' => $this->getContactUsContentEn()
                ],
                'excerpt' => [
                    'ar' => 'تواصل معنا للحصول على المساعدة والدعم',
                    'en' => 'Get in touch with us for help and support'
                ],
                'meta_title' => [
                    'ar' => 'اتصل بنا - نحن هنا لمساعدتك',
                    'en' => 'Contact Us - We\'re Here to Help'
                ],
                'meta_description' => [
                    'ar' => 'تواصل معنا عبر البريد الإلكتروني أو الهاتف للحصول على الدعم والمساعدة',
                    'en' => 'Contact us via email or phone for support and assistance'
                ],
                'status' => 'published',
                'sort_order' => 4
            ],
            [
                'title' => [
                    'ar' => 'الأسئلة الشائعة',
                    'en' => 'Frequently Asked Questions'
                ],
                'slug' => 'faq',
                'content' => [
                    'ar' => $this->getFaqContentAr(),
                    'en' => $this->getFaqContentEn()
                ],
                'excerpt' => [
                    'ar' => 'إجابات على الأسئلة الأكثر شيوعاً',
                    'en' => 'Answers to the most frequently asked questions'
                ],
                'meta_title' => [
                    'ar' => 'الأسئلة الشائعة - إجابات سريعة',
                    'en' => 'FAQ - Quick Answers'
                ],
                'meta_description' => [
                    'ar' => 'اعثر على إجابات للأسئلة الشائعة حول خدماتنا ومنتجاتنا',
                    'en' => 'Find answers to frequently asked questions about our services and products'
                ],
                'status' => 'published',
                'sort_order' => 5
            ]
        ];

        foreach ($basicPages as $pageData) {
            // Check if page already exists
            $existingPage = Page::where('slug', $pageData['slug'])->first();

            if (!$existingPage) {
                Page::create([
                    'title' => $pageData['title'],
                    'slug' => $pageData['slug'],
                    'content' => $pageData['content'],
                    'excerpt' => $pageData['excerpt'],
                    'meta_title' => $pageData['meta_title'],
                    'meta_description' => $pageData['meta_description'],
                    'status' => $pageData['status'],
                    'sort_order' => $pageData['sort_order'],
                    'published_at' => now(),
                    'created_by' => $adminUser->id,
                    'updated_by' => $adminUser->id,
                ]);

                $this->command->info("Created page: {$pageData['slug']}");
            } else {
                $this->command->info("Page already exists: {$pageData['slug']}");
            }
        }
    }

    private function getPrivacyPolicyContentAr()
    {
        return '<h2>سياسة الخصوصية</h2>
        <p>نحن نقدر خصوصيتك ونلتزم بحماية معلوماتك الشخصية. توضح هذه السياسة كيفية جمع واستخدام وحماية بياناتك.</p>

        <h3>المعلومات التي نجمعها</h3>
        <ul>
            <li>المعلومات الشخصية مثل الاسم والبريد الإلكتروني</li>
            <li>معلومات الاستخدام والتفاعل مع الموقع</li>
            <li>معلومات تقنية مثل عنوان IP ونوع المتصفح</li>
        </ul>

        <h3>كيفية استخدام المعلومات</h3>
        <p>نستخدم المعلومات المجمعة لتحسين خدماتنا وتقديم تجربة أفضل للمستخدمين.</p>

        <h3>حماية البيانات</h3>
        <p>نتخذ إجراءات أمنية صارمة لحماية بياناتك من الوصول غير المصرح به.</p>';
    }

    private function getPrivacyPolicyContentEn()
    {
        return '<h2>Privacy Policy</h2>
        <p>We value your privacy and are committed to protecting your personal information. This policy explains how we collect, use, and protect your data.</p>

        <h3>Information We Collect</h3>
        <ul>
            <li>Personal information such as name and email address</li>
            <li>Usage information and interaction with the website</li>
            <li>Technical information such as IP address and browser type</li>
        </ul>

        <h3>How We Use Information</h3>
        <p>We use the collected information to improve our services and provide a better user experience.</p>

        <h3>Data Protection</h3>
        <p>We implement strict security measures to protect your data from unauthorized access.</p>';
    }

    private function getTermsOfServiceContentAr()
    {
        return '<h2>شروط الاستخدام</h2>
        <p>مرحباً بك في موقعنا. باستخدام هذا الموقع، فإنك توافق على الالتزام بهذه الشروط والأحكام.</p>

        <h3>قبول الشروط</h3>
        <p>باستخدام خدماتنا، فإنك توافق على جميع الشروط والأحكام المذكورة هنا.</p>

        <h3>استخدام الخدمة</h3>
        <ul>
            <li>يجب استخدام الخدمة للأغراض القانونية فقط</li>
            <li>لا يُسمح بانتهاك حقوق الآخرين</li>
            <li>يُمنع نشر محتوى مسيء أو غير لائق</li>
        </ul>

        <h3>المسؤولية</h3>
        <p>نحن غير مسؤولين عن أي أضرار قد تنتج عن استخدام الخدمة.</p>';
    }

    private function getTermsOfServiceContentEn()
    {
        return '<h2>Terms of Service</h2>
        <p>Welcome to our website. By using this site, you agree to comply with these terms and conditions.</p>

        <h3>Acceptance of Terms</h3>
        <p>By using our services, you agree to all terms and conditions mentioned here.</p>

        <h3>Use of Service</h3>
        <ul>
            <li>Service must be used for legal purposes only</li>
            <li>Violation of others\' rights is not permitted</li>
            <li>Publishing offensive or inappropriate content is prohibited</li>
        </ul>

        <h3>Liability</h3>
        <p>We are not responsible for any damages that may result from using the service.</p>';
    }

    private function getAboutUsContentAr()
    {
        return '<h2>من نحن</h2>
        <p>نحن شركة رائدة في مجال التكنولوجيا، نسعى لتقديم أفضل الحلول والخدمات لعملائنا.</p>

        <h3>رؤيتنا</h3>
        <p>أن نكون الخيار الأول للعملاء في مجال التكنولوجيا والابتكار.</p>

        <h3>رسالتنا</h3>
        <p>تقديم حلول تقنية مبتكرة تساعد عملاءنا على تحقيق أهدافهم وتطوير أعمالهم.</p>

        <h3>قيمنا</h3>
        <ul>
            <li>الجودة والتميز في كل ما نقدمه</li>
            <li>الشفافية والصدق في التعامل</li>
            <li>الابتكار والتطوير المستمر</li>
            <li>خدمة العملاء المتميزة</li>
        </ul>';
    }

    private function getAboutUsContentEn()
    {
        return '<h2>About Us</h2>
        <p>We are a leading technology company, striving to provide the best solutions and services to our clients.</p>

        <h3>Our Vision</h3>
        <p>To be the first choice for customers in the field of technology and innovation.</p>

        <h3>Our Mission</h3>
        <p>To provide innovative technical solutions that help our clients achieve their goals and develop their businesses.</p>

        <h3>Our Values</h3>
        <ul>
            <li>Quality and excellence in everything we offer</li>
            <li>Transparency and honesty in dealings</li>
            <li>Innovation and continuous development</li>
            <li>Outstanding customer service</li>
        </ul>';
    }

    private function getContactUsContentAr()
    {
        return '<h2>اتصل بنا</h2>
        <p>نحن هنا لمساعدتك! لا تتردد في التواصل معنا للحصول على الدعم أو الإجابة على استفساراتك.</p>

        <h3>معلومات الاتصال</h3>
        <div class="row">
            <div class="col-md-6">
                <h4>البريد الإلكتروني</h4>
                <p><strong>الدعم العام:</strong> <EMAIL></p>
                <p><strong>المبيعات:</strong> <EMAIL></p>
                <p><strong>الدعم الفني:</strong> <EMAIL></p>
            </div>
            <div class="col-md-6">
                <h4>الهاتف</h4>
                <p><strong>خدمة العملاء:</strong> +966 11 123 4567</p>
                <p><strong>المبيعات:</strong> +966 11 123 4568</p>
                <p><strong>الطوارئ:</strong> +966 50 123 4569</p>
            </div>
        </div>

        <h3>العنوان</h3>
        <p>الرياض، المملكة العربية السعودية<br>
        ص.ب 12345، الرياض 11564</p>

        <h3>ساعات العمل</h3>
        <p><strong>الأحد - الخميس:</strong> 9:00 ص - 6:00 م<br>
        <strong>الجمعة - السبت:</strong> مغلق</p>';
    }

    private function getContactUsContentEn()
    {
        return '<h2>Contact Us</h2>
        <p>We\'re here to help! Don\'t hesitate to contact us for support or to answer your questions.</p>

        <h3>Contact Information</h3>
        <div class="row">
            <div class="col-md-6">
                <h4>Email</h4>
                <p><strong>General Support:</strong> <EMAIL></p>
                <p><strong>Sales:</strong> <EMAIL></p>
                <p><strong>Technical Support:</strong> <EMAIL></p>
            </div>
            <div class="col-md-6">
                <h4>Phone</h4>
                <p><strong>Customer Service:</strong> +966 11 123 4567</p>
                <p><strong>Sales:</strong> +966 11 123 4568</p>
                <p><strong>Emergency:</strong> +966 50 123 4569</p>
            </div>
        </div>

        <h3>Address</h3>
        <p>Riyadh, Saudi Arabia<br>
        P.O. Box 12345, Riyadh 11564</p>

        <h3>Business Hours</h3>
        <p><strong>Sunday - Thursday:</strong> 9:00 AM - 6:00 PM<br>
        <strong>Friday - Saturday:</strong> Closed</p>';
    }

    private function getFaqContentAr()
    {
        return '<h2>الأسئلة الشائعة</h2>
        <p>إليك إجابات على الأسئلة الأكثر شيوعاً التي نتلقاها من عملائنا.</p>

        <div class="accordion" id="faqAccordion">
            <div class="accordion-item">
                <h3>كيف يمكنني إنشاء حساب جديد؟</h3>
                <p>يمكنك إنشاء حساب جديد بالنقر على زر "تسجيل" في أعلى الصفحة وملء النموذج المطلوب.</p>
            </div>

            <div class="accordion-item">
                <h3>كيف يمكنني استرداد كلمة المرور؟</h3>
                <p>انقر على "نسيت كلمة المرور" في صفحة تسجيل الدخول وأدخل بريدك الإلكتروني لتلقي رابط إعادة التعيين.</p>
            </div>

            <div class="accordion-item">
                <h3>ما هي طرق الدفع المتاحة؟</h3>
                <p>نقبل جميع بطاقات الائتمان الرئيسية، التحويل البنكي، والدفع عند الاستلام في بعض المناطق.</p>
            </div>

            <div class="accordion-item">
                <h3>كم تستغرق عملية التوصيل؟</h3>
                <p>عادة ما تستغرق عملية التوصيل من 2-5 أيام عمل حسب موقعك الجغرافي.</p>
            </div>
        </div>';
    }

    private function getFaqContentEn()
    {
        return '<h2>Frequently Asked Questions</h2>
        <p>Here are answers to the most common questions we receive from our customers.</p>

        <div class="accordion" id="faqAccordion">
            <div class="accordion-item">
                <h3>How can I create a new account?</h3>
                <p>You can create a new account by clicking the "Register" button at the top of the page and filling out the required form.</p>
            </div>

            <div class="accordion-item">
                <h3>How can I recover my password?</h3>
                <p>Click "Forgot Password" on the login page and enter your email to receive a reset link.</p>
            </div>

            <div class="accordion-item">
                <h3>What payment methods are available?</h3>
                <p>We accept all major credit cards, bank transfers, and cash on delivery in some areas.</p>
            </div>

            <div class="accordion-item">
                <h3>How long does delivery take?</h3>
                <p>Delivery usually takes 2-5 business days depending on your location.</p>
            </div>
        </div>';
    }
}
