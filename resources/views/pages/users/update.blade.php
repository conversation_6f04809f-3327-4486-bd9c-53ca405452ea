<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Edit User') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <form wire:submit.prevent="update">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" wire:model="name"
                                    placeholder="{{ __('Enter name') }}">
                                @error('name')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" wire:model="email"
                                    placeholder="{{ __('Enter email') }}">
                                @error('email')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Password') }} <small
                                        class="text-muted">({{ __('Leave empty to keep current') }})</small></label>
                                <input type="password" class="form-control" wire:model="password"
                                    placeholder="{{ __('Enter new password') }}">
                                @error('password')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        @if (!$user->hasRole(['super_admin']))
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label">{{ __('Role') }} <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" wire:model="role">
                                        <option value="">{{ __('Select Role') }}</option>
                                        @foreach ($roles as $role_item)
                                            @if ($role_item->name != 'super_admin')
                                                <option value="{{ $role_item->name }}"
                                                    {{ $role == $role_item->name ? 'selected' : '' }}>
                                                    {{ ucfirst($role_item->name) }}
                                                </option>
                                            @endif
                                        @endforeach
                                    </select>
                                    @error('role')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        @else
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label">{{ __('Role') }}</label>
                                    <input type="text" class="form-control" value="{{ ucfirst($role) }}" readonly>
                                    <small class="text-muted">{{ __('Super Admin role cannot be changed') }}</small>
                                </div>
                            </div>
                        @endif
                        <div class="col-md-12">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Profile Photo') }}</label>
                                <input type="file" class="form-control" wire:model="profilePhoto" accept="image/*">
                                @error('profilePhoto')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror

                                <div class="mt-2">
                                    @if ($profilePhoto)
                                        <div class="d-flex align-items-center gap-3">
                                            <div>
                                                <strong>{{ __('New Photo Preview:') }}</strong>
                                                <img src="{{ $profilePhoto->temporaryUrl() }}" alt="New Preview" class="img-thumbnail d-block mt-1" style="max-width: 150px; max-height: 150px;">
                                            </div>
                                            @if ($user && $user->profile_photo_url)
                                                <div>
                                                    <strong>{{ __('Current Photo:') }}</strong>
                                                    <img src="{{ $user->profile_photo_url }}" alt="Current Photo" class="img-thumbnail d-block mt-1" style="max-width: 150px; max-height: 150px;">
                                                </div>
                                            @endif
                                        </div>
                                    @elseif ($user && $user->profile_photo_url)
                                        <div>
                                            <strong>{{ __('Current Photo:') }}</strong>
                                            <img src="{{ $user->profile_photo_url }}" alt="Current Photo" class="img-thumbnail d-block mt-1" style="max-width: 150px; max-height: 150px;">
                                            <button type="button" class="btn btn-sm btn-outline-danger mt-2" wire:click="deleteProfilePhoto({{ $user->id }})">
                                                <i class="fas fa-trash"></i> {{ __('Delete Photo') }}
                                            </button>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Update') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
