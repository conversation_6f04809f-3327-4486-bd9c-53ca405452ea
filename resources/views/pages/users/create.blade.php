<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Add New User') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <form wire:submit.prevent="store">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" wire:model="name"
                                    placeholder="{{ __('Enter name') }}">
                                @error('name')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" wire:model="email"
                                    placeholder="{{ __('Enter email') }}">
                                @error('email')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Password') }} <span
                                        class="text-danger">*</span></label>
                                <input type="password" class="form-control" wire:model="password"
                                    placeholder="{{ __('Enter password') }}">
                                @error('password')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Role') }} <span
                                        class="text-danger">*</span></label>
                                <select class="form-select" wire:model="role">
                                    <option value="">{{ __('Select Role') }}</option>
                                    @foreach ($roles as $role_item)
                                        @if ($role_item->name != 'super_admin')
                                            <option value="{{ $role_item->name }}">{{ ucfirst($role_item->name) }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('role')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-4">
                                <label class="form-label">{{ __('Profile Photo') }}</label>
                                <input type="file" class="form-control" wire:model="profilePhoto" accept="image/*">
                                @error('profilePhoto')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                                @if ($profilePhoto)
                                    <div class="mt-2">
                                        <img src="{{ $profilePhoto->temporaryUrl() }}" alt="Preview" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
