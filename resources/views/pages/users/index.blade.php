<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('users') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('users') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_user') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('users_list') }}</h5>
                                <div class="card-header-action">
                                    <div class="card-header-btn">
                                        {{-- <div class="dropdown">
                                            <a class="btn btn-sm btn-icon btn-light-brand" data-bs-toggle="dropdown">
                                                <i class="feather-more-vertical"></i>
                                            </a>
                                            <div class="dropdown-menu dropdown-menu-end">
                                                <a href="javascript:void(0);" class="dropdown-item">
                                                    <i class="feather-download me-3"></i>
                                                    <span>{{ __('Export') }}</span>
                                                </a>
                                                <a href="javascript:void(0);" class="dropdown-item">
                                                    <i class="feather-printer me-3"></i>
                                                    <span>{{ __('Print') }}</span>
                                                </a>
                                            </div>
                                        </div> --}}
                                    </div>
                                </div>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <!-- Filters -->
                                <div class="p-3 border-bottom">
                                    <div class="row g-3 d-flex justify-content-between">
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('search') }}</label>
                                            <input type="text" class="form-control"
                                                wire:model.live.debounce.300ms="search"
                                                placeholder="{{ __('search_name_email_phone') }}...">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('Role') }}</label>
                                            <select class="form-select" wire:model.live="selectedRole">
                                                <option value="">{{ __('all_roles') }}</option>
                                                @foreach ($roles as $role)
                                                    <option value="{{ $role->name }}">{{ $role->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        {{-- <div class="col-md-2">
                                            <label class="form-label">{{ __('from_date') }}</label>
                                            <input type="date" class="form-control" wire:model.live="dateFrom">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('to_date') }}</label>
                                            <input type="date" class="form-control" wire:model.live="dateTo">
                                        </div> --}}
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('per_page') }}</label>
                                            <select class="form-select" wire:model.live="perPage">
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1 d-flex align-items-end">
                                            <button type="button" class="btn btn-secondary" wire:click="resetFilters"
                                                title="{{ __('reset_filters') }}">
                                                <i class="feather-refresh-cw"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th class="text-center">{{ __('name') }}</th>
                                                <th class="text-center">{{ __('email') }}</th>
                                                <th class="text-center">{{ __('phone') }}</th>
                                                <th class="text-center">{{ __('Role') }}</th>
                                                <th class="text-center">{{ __('created_at') }}</th>
                                                <th class="text-center">{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($users as $user)
                                                <tr>
                                                    <td class="text-center">
                                                        <div class="d-flex align-items-center gap-3">
                                                            <div class="avatar-image avatar-md">
                                                                @if($user->profile_photo_url)
                                                                    <img src="{{ $user->profile_photo_url }}" alt="{{ $user->name }}" class="img-fluid rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                                @else
                                                                    <img src="{{ asset('assets/images/avatar/1.png') }}" alt="" class="img-fluid">
                                                                @endif
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold text-dark">{{ $user->name }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">{{ $user->email }}</td>
                                                    <td class="text-center">{{ $user->phone ?? '-' }}</td>
                                                    <td class="text-center">
                                                        @if ($user->roles->count() > 0)
                                                            <span
                                                                class="badge bg-soft-primary text-primary">{{ $user->roles->first()->name }}</span>
                                                        @else
                                                            <span
                                                                class="badge bg-soft-secondary text-secondary">{{ __('No Role') }}</span>
                                                        @endif
                                                    </td>
                                                    <td class="text-center">{{ $user->created_at->format('Y-m-d') }}
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="hstack gap-2 justify-content-center">
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md"
                                                                wire:click="show({{ $user->id }})">
                                                                <i class="feather-eye"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md"
                                                                wire:click="edit({{ $user->id }})">
                                                                <i class="feather-edit-3"></i>
                                                            </a>
                                                            @if (!$user->hasRole(['admin', 'super_admin']))
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md"
                                                                wire:click="confirmDelete({{ $user->id }})">
                                                                <i class="feather-trash-2"></i>
                                                            </a>
                                                            @else
                                                            <span class="avatar-text avatar-md text-muted" title="{{ __('Cannot delete admin users') }}">
                                                                <i class="feather-shield"></i>
                                                            </span>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="6" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-users fs-1 mb-3"></i>
                                                            <p>{{ __('no_data') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if ($users->hasPages())
                                <div class="card-footer">
                                    {{ $users->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($showCreateModal)
                @include('pages.users.create')
            @endif

            @if ($showEditModal)
                @include('pages.users.update')
            @endif

            @if ($showViewModal)
                @include('pages.users.show')
            @endif

            @if ($showDeleteModal)
                @include('pages.users.delete')
            @endif
        </div>
    </main>
</div>
