<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('buy_card') }} - {{ $userStats['user']->name }}</h5>
                <button type="button" class="btn-close" wire:click="closeBuyCardModal"></button>
            </div>
            <div class="modal-body">
                <!-- User Statistics -->
                <div class="row mb-4">
                    <div class="col-12">
                        {{-- <h6 class="text-primary mb-3">{{ __('user_statistics') }}</h6> --}}
                        <div class="row g-3">
                            <!-- Current Balance -->
                            <div class="col-md-12">
                                <div class="card bg-soft-success">
                                    <div class="card-body text-center">
                                        <h4 class="text-success mb-1">{{ $userStats['current_balance'] }}</h4>
                                        <p class="text-muted mb-0">{{ __('current_points_balance') }}</p>
                                        @if($userStats['pending_points'] > 0)
                                            <small class="text-warning d-block mt-2">
                                                <i class="feather-clock me-1"></i>
                                                {{ __('pending_points') }}: {{ $userStats['pending_points'] }}
                                            </small>
                                            <hr class="my-2">
                                            <strong class="text-primary">
                                                {{ __('total_points') }}: {{ $userStats['total_points'] }}
                                            </strong>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            {{-- <!-- Order Statistics -->
                            <div class="col-md-4">
                                <div class="card bg-soft-warning">
                                    <div class="card-body text-center">
                                        <h4 class="text-warning mb-1">{{ $userStats['order_stats']['created_orders'] }}</h4>
                                        <p class="text-muted mb-0">{{ __('created_orders') }}</p>
                                        <small class="text-muted">
                                            {{ __('delivered') }}: {{ $userStats['order_stats']['delivered_orders'] }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Statistics -->
                            <div class="col-md-4">
                                <div class="card bg-soft-primary">
                                    <div class="card-body text-center">
                                        <h4 class="text-primary mb-1">{{ $userStats['delivery_stats']['delivered_orders'] }}</h4>
                                        <p class="text-muted mb-0">{{ __('delivered_orders') }}</p>
                                        <small class="text-muted">
                                            {{ __('current_deliveries') }}: {{ $userStats['delivery_stats']['current_deliveries'] }}
                                        </small>
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>
                </div>

                <!-- Conversion Rate Alert -->
                <div class="alert alert-danger mb-4">
                    <i class="feather-info me-2"></i>
                    {{ __('conversion_rate_info') }}:
                    {{-- <strong>1 {{ __('currency_unit') }} = {{ $userStats['conversion_rates']['cash_to_points_rate'] }} {{ __('points') }}</strong> --}}
                    {{-- <br> --}}
                    <strong>{{ $userStats['conversion_rates']['points_to_cash_rate'] }} {{ __('points') }} = 1 {{ __('currency_unit') }}</strong>
                </div>

                <!-- Buy Card Form -->
                <form wire:submit.prevent="buyCard">
                    <div class="row g-3">
                        <!-- Card Name -->
                        <div class="col-12">
                            <label class="form-label">{{ __('card_name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('cardName') is-invalid @enderror"
                                   wire:model="cardName" placeholder="{{ __('enter_card_name') }}">
                            @error('cardName')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Cash and Points Inputs -->
                        <div class="col-md-6">
                            <label class="form-label">{{ __('cash_amount') }} ({{ __('currency_unit') }})</label>
                            <input type="number" step="0.01" min="0" class="form-control"
                                   wire:model.live="cashAmount" placeholder="0.00">
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">{{ __('points_amount') }} <span class="text-danger">*</span></label>
                            <input type="number" min="1" class="form-control @error('pointsAmount') is-invalid @enderror"
                                   wire:model.live="pointsAmount" placeholder="0">
                            @error('pointsAmount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Balance Check -->
                        @if($pointsAmount && $pointsAmount > $userStats['total_points'])
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <i class="feather-alert-triangle me-2"></i>
                                    {{ __('insufficient_points_warning') }}
                                    ({{ __('required') }}: {{ $pointsAmount }}, {{ __('available') }}: {{ $userStats['total_points'] }})
                                </div>
                            </div>
                        @endif
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeBuyCardModal">
                            {{ __('cancel') }}
                        </button>
                        <button type="submit" class="btn btn-primary"
                                @if($pointsAmount && $pointsAmount > $userStats['total_points']) disabled @endif>
                            <i class="feather-credit-card me-2"></i>
                            {{ __('buy_card') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Backdrop -->
<div class="modal-backdrop fade show"></div>
