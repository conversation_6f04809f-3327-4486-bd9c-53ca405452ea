<main class="nxl-container">
    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">{{ __('buy_cards') }}</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('dashboard') }}</a></li>
                    <li class="breadcrumb-item">{{ __('buy_cards') }}</li>
                </ul>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content">
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">{{ __('users_list') }}</h5>
                            <div class="card-header-action">
                                <div class="card-header-btn">
                                    <div data-bs-toggle="tooltip" title="{{ __('collapse') }}">
                                        <a href="javascript:void(0);" class="avatar-text avatar-xs bg-success"
                                            data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false"
                                            aria-controls="collapseExample">
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body custom-card-action p-0">
                            <!-- Filters -->
                            <div class="p-3 border-bottom">
                                <div class="row g-3 d-flex justify-content-between">
                                    <div class="col-md-3">
                                        <label class="form-label">{{ __('search') }}</label>
                                        <input type="text" class="form-control" wire:model.live.debounce.300ms="search"
                                               placeholder="{{ __('search_name_email_phone') }}...">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">{{ __('Role') }}</label>
                                        <select class="form-select" wire:model.live="selectedRole">
                                            <option value="">{{ __('all_roles') }}</option>
                                            @foreach($roles as $role)
                                                <option value="{{ $role->name }}">{{ $role->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    {{-- <div class="col-md-2">
                                        <label class="form-label">{{ __('from_date') }}</label>
                                        <input type="date" class="form-control" wire:model.live="dateFrom">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">{{ __('to_date') }}</label>
                                        <input type="date" class="form-control" wire:model.live="dateTo">
                                    </div> --}}
                                    <div class="col-md-2">
                                        <label class="form-label">{{ __('per_page') }}</label>
                                        <select class="form-select" wire:model.live="perPage">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="button" class="btn btn-secondary" wire:click="resetFilters" title="{{ __('reset_filters') }}">
                                            <i class="feather-refresh-cw"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover table-bordered mb-0 text-center">
                                    <thead>
                                        <tr>
                                            <th class="text-center">{{ __('name') }}</th>
                                            <th class="text-center">{{ __('email') }}</th>
                                            <th class="text-center">{{ __('phone') }}</th>
                                            <th class="text-center">{{ __('Role') }}</th>
                                            <th class="text-center">{{ __('current_balance') }}</th>
                                            <th class="text-center">{{ __('actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($users as $user)
                                            <tr>
                                                <td class="text-center">
                                                    <div class="d-flex align-items-center gap-3">
                                                        <div class="avatar-image avatar-md">
                                                            <img src="{{ asset('assets/images/avatar/1.png') }}"
                                                                alt="" class="img-fluid">
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold text-dark">{{ $user->name }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center">{{ $user->email }}</td>
                                                <td class="text-center">{{ $user->phone ?? '-' }}</td>
                                                <td class="text-center">
                                                    @if ($user->roles->count() > 0)
                                                        <span
                                                            class="badge bg-soft-primary text-primary">{{ $user->roles->first()->name }}</span>
                                                    @else
                                                        <span
                                                            class="badge bg-soft-secondary text-secondary">{{ __('No Role') }}</span>
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-soft-success text-success">
                                                        {{ $user->points->current_balance ?? 0 }} {{ __('points') }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <div class="hstack gap-2 justify-content-center">
                                                        <button type="button" class="btn btn-primary btn-sm"
                                                                wire:click="openBuyCardModal({{ $user->id }})"
                                                                title="{{ __('buy_card') }}">
                                                            <i class="feather-credit-card me-1"></i>
                                                            {{ __('buy_card') }}
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center py-4">
                                                    <div class="text-muted">{{ __('no_data_found') }}</div>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-center">
                {{ $users->links() }}
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>

    <!-- Buy Card Modal -->
    @if ($showBuyCardModal)
        @include('pages.buy-card.buy-modal')
    @endif
</main>
