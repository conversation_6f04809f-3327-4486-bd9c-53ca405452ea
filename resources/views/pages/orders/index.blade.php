<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('orders') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('orders') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <button type="button" class="btn btn-secondary" wire:click="refreshStatistics">
                                <i class="feather-refresh-cw me-2"></i>
                                {{ __('refresh') }}
                            </button>
                            {{-- <button type="button" class="btn btn-primary" wire:click="exportOrders">
                                <i class="feather-download me-2"></i>
                                {{ __('export') }}
                            </button> --}}
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <!-- Statistics Cards -->
                    <div class="col-lg-12 mb-4">
                        <div class="row">
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-gray-200">
                                                <i class="feather-package text-dark"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['total'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('total_orders') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-warning-subtle">
                                                <i class="feather-clock text-warning"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['pending'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('pending') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-info-subtle">
                                                <i class="feather-check-circle text-info"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['confirmed'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('confirmed') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-primary-subtle">
                                                <i class="feather-truck text-primary"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['picked_up'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('picked_up') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-success-subtle">
                                                <i class="feather-check-square text-success"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['delivered'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('delivered') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-danger-subtle">
                                                <i class="feather-x-circle text-danger"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['canceled'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('canceled') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stuck Orders Statistics -->
                    <div class="col-lg-12 mb-4">
                        <div class="row">
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-warning-subtle">
                                                <i class="feather-alert-triangle text-warning"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($statistics['stuck'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('stuck_orders') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('orders_list') }}</h5>
                                <div class="card-header-action">
                                    <div class="card-header-btn">
                                        {{-- <div class="dropdown">
                                            <a class="btn btn-sm btn-icon btn-light-brand" data-bs-toggle="dropdown">
                                                <i class="feather-more-vertical"></i>
                                            </a>
                                            <div class="dropdown-menu dropdown-menu-end">
                                                <a href="javascript:void(0);" class="dropdown-item"
                                                    wire:click="exportOrders">
                                                    <i class="feather-download me-3"></i>
                                                    <span>{{ __('export') }}</span>
                                                </a>
                                                <a href="javascript:void(0);" class="dropdown-item">
                                                    <i class="feather-printer me-3"></i>
                                                    <span>{{ __('print') }}</span>
                                                </a>
                                            </div>
                                        </div> --}}
                                    </div>
                                </div>
                            </div>

                            <!-- Tabs -->
                            <div class="card-body border-bottom">
                                <ul class="nav nav-tabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link {{ $activeTab === 'all' ? 'active' : '' }}"
                                                wire:click="switchTab('all')"
                                                type="button">
                                            <i class="feather-list me-2"></i>
                                            {{ __('all_orders') }}
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link {{ $activeTab === 'stuck' ? 'active' : '' }}"
                                                wire:click="switchTab('stuck')"
                                                type="button">
                                            <i class="feather-alert-triangle me-2"></i>
                                            {{ __('stuck_orders') }}
                                            @if(isset($statistics['stuck']) && $statistics['stuck'] > 0)
                                                <span class="badge bg-danger ms-1">{{ $statistics['stuck'] }}</span>
                                            @endif
                                        </button>
                                    </li>
                                </ul>
                            </div>

                            <div class="card-body custom-card-action p-0">
                                <!-- Filters (only show for all orders tab) -->
                                @if($activeTab === 'all')
                                    <div class="p-3 border-bottom">
                                        <div class="row g-3 d-flex justify-content-between">
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('search') }}</label>
                                            <input type="text" class="form-control"
                                                wire:model.live.debounce.300ms="search"
                                                placeholder="{{ __('search_orders') }}">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('status') }}</label>
                                            <select class="form-select" wire:model.live="status">
                                                <option value="">{{ __('all') }}</option>
                                                @foreach ($statusOptions as $value => $label)
                                                    <option value="{{ $value }}">{{ $label }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('from_station') }}</label>
                                            <select class="form-select" wire:model.live="fromStationId">
                                                <option value="">{{ __('all') }}</option>
                                                @foreach ($stations as $station)
                                                    <option value="{{ $station->id }}">{{ $station->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('to_station') }}</label>
                                            <select class="form-select" wire:model.live="toStationId">
                                                <option value="">{{ __('all') }}</option>
                                                @foreach ($stations as $station)
                                                    <option value="{{ $station->id }}">{{ $station->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('per_page') }}</label>
                                            <select class="form-select" wire:model.live="perPage">
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1 d-flex align-items-end">
                                            <button type="button" class="btn btn-secondary"
                                                wire:click="clearFilters" title="{{ __('reset_filters') }}">
                                                <i class="feather-refresh-cw"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row g-3 mt-2">
                                        <div class="col-md-2">
                                            <label class="form-label">{{ __('type') }}</label>
                                            <select class="form-select" wire:model.live="typeId">
                                                <option value="">{{ __('all') }}</option>
                                                @foreach ($types as $type)
                                                    <option value="{{ $type->id }}">{{ $type->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('from_date') }}</label>
                                            <input type="date" class="form-control" wire:model.live="dateFrom">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('to_date') }}</label>
                                            <input type="date" class="form-control" wire:model.live="dateTo">
                                        </div>
                                        </div>
                                    </div>
                                @endif

                                <div class="table-responsive">
                                    @if ($orders->count() > 0)
                                        <table class="table table-hover table-bordered mb-0 text-center">
                                            <thead>
                                                <tr>
                                                    <th class="text-center">{{ __('barcode') }}</th>
                                                    <th class="text-center">{{ __('user') }}</th>
                                                    <th class="text-center">{{ __('route') }}</th>
                                                    <th class="text-center">{{ __('receiver') }}</th>
                                                    <th class="text-center">{{ __('price') }}</th>
                                                    <th class="text-center">{{ __('status') }}</th>
                                                    <th class="text-center">{{ __('created_at') }}</th>
                                                    <th class="text-center">{{ __('actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($orders as $order)
                                                    <tr>
                                                        <td class="text-center">
                                                            <span
                                                                class="badge bg-primary">{{ $order->barcode }}</span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="d-flex align-items-center gap-3">
                                                                <div class="avatar-text avatar-md bg-primary">
                                                                    <i class="feather-user text-white"></i>
                                                                </div>
                                                                <div>
                                                                    <div class="fw-bold text-dark">
                                                                        {{ $order->user->name ?? __('unknown') }}</div>
                                                                    <div class="fs-12 fw-normal text-muted">
                                                                        {{ $order->user->phone ?? '' }}</div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <div
                                                                class="d-flex align-items-center justify-content-center">
                                                                <span
                                                                    class="badge bg-info me-1">{{ $order->fromStation->name ?? __('unknown') }}</span>
                                                                @if ($availableLanguages->where('code', app()->getLocale())->first()?->direction == 'rtl')
                                                                    <i class="feather-arrow-left mx-1"></i>
                                                                @else
                                                                    <i class="feather-arrow-right mx-1"></i>
                                                                @endif
                                                                <span
                                                                    class="badge bg-success">{{ $order->toStation->name ?? __('unknown') }}</span>
                                                            </div>
                                                            <small
                                                                class="text-muted d-block">{{ $order->type->name ?? __('unknown') }}</small>
                                                        </td>
                                                        <td class="text-center">
                                                            <div>
                                                                <div class="fw-bold text-dark">
                                                                    {{ $order->receiver_name }}</div>
                                                                <div class="fs-12 fw-normal text-muted">
                                                                    {{ $order->receiver_phone }}</div>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span
                                                                class="fw-bold">{{ number_format($order->price, 2) }}
                                                                {{ __('currency_unit') }}</span>
                                                            @if ($order->points_used > 0)
                                                                <small
                                                                    class="text-info d-block">{{ $order->points_used }}
                                                                    {{ __('points') }}</small>
                                                            @endif
                                                        </td>
                                                        <td class="text-center">
                                                            @php
                                                                $status = \App\Enums\OrderStatus::from($order->status);
                                                            @endphp
                                                            <span class="badge bg-{{ $status->color() }}">
                                                                <i class="{{ $status->icon() }} me-1"></i>
                                                                {{ $status->label() }}
                                                            </span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="fw-bold text-dark">
                                                                {{ $order->created_at->format('Y-m-d') }}</div>
                                                            <div class="fs-12 fw-normal text-muted">
                                                                {{ $order->created_at->format('H:i') }}</div>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="hstack gap-2 justify-content-center">
                                                                <a href="{{ route('orders.show', $order->id) }}"
                                                                    class="avatar-text avatar-md bg-info text-white"
                                                                    data-bs-toggle="tooltip"
                                                                    title="{{ __('view') }}">
                                                                    <i class="feather-eye"></i>
                                                                </a>
                                                                {{-- @can('orders.edit')
                                                                    <button class="avatar-text avatar-md bg-warning text-white" wire:click="editOrder({{ $order->id }})" data-bs-toggle="tooltip" title="{{ __('edit') }}">
                                                                        <i class="feather-edit"></i>
                                                                    </button>
                                                                @endcan
                                                                @can('orders.delete')
                                                                    <button class="avatar-text avatar-md bg-danger text-white"
                                                                            wire:click="deleteOrder({{ $order->id }})"
                                                                            data-bs-toggle="tooltip" title="{{ __('delete') }}">
                                                                        <i class="feather-trash-2"></i>
                                                                    </button>
                                                                @endcan --}}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    @else
                                        <div class="text-center py-5">
                                            <div class="avatar-text avatar-xl bg-gray-200 mb-3 mx-auto">
                                                <i class="feather-package"></i>
                                            </div>
                                            <h5>{{ __('no_orders_found') }}</h5>
                                            <p class="text-muted">{{ __('no_orders_match_criteria') }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            @if ($orders->hasPages())
                                <div class="card-footer">
                                    {{ $orders->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>
