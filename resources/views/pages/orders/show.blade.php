<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('order') }}: {{ $order->barcode }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('orders.index') }}">{{ __('orders') }}</a></li>
                        <li class="breadcrumb-item">{{ $order->barcode }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <button type="button" class="btn btn-secondary" wire:click="refreshOrder">
                                <i class="feather-refresh-cw me-2"></i>
                                {{ __('refresh') }}
                            </button>
                            @can('orders.edit')
                                <button type="button" class="btn btn-primary" wire:click="openStatusModal">
                                    <i class="feather-edit me-2"></i>
                                    {{ __('update_status') }}
                                </button>
                            @endcan
                            {{-- <button type="button" class="btn btn-info" wire:click="printOrder">
                                <i class="feather-printer me-2"></i>
                                {{ __('print') }}
                            </button>
                            @can('orders.delete')
                                <button type="button" class="btn btn-danger" wire:click="deleteOrder">
                                    <i class="feather-trash-2 me-2"></i>
                                    {{ __('delete') }}
                                </button>
                            @endcan --}}
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <!-- Order Details -->
                    <div class="col-lg-8">
                        <!-- Order Info Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('order_details') }}</h5>
                                <div class="card-header-action">
                                    <span class="badge bg-{{ $this->statusBadge['color'] }} fs-12">
                                        <i class="{{ $this->statusBadge['icon'] }} me-1"></i>
                                        {{ $this->statusBadge['label'] }}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">{{ __('barcode') }}</label>
                                            <div class="fw-bold fs-5">{{ $order->barcode }}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">{{ __('user') }}</label>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-text avatar-md bg-primary me-3">
                                                    <i class="feather-user text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold">{{ $order->user->name ?? __('unknown') }}
                                                    </div>
                                                    <small class="text-muted">{{ $order->user->phone ?? '' }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">{{ __('receiver') }}</label>
                                            <div>
                                                <div class="fw-semibold">{{ $order->receiver_name }}</div>
                                                <small class="text-muted">{{ $order->receiver_phone }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">{{ __('route') }}</label>
                                            <div class="d-flex align-items-center">
                                                <span
                                                    class="badge bg-info me-2">{{ $order->fromStation->name ?? __('unknown') }}</span>
                                                <i class="feather-arrow-right mx-2"></i>
                                                <span
                                                    class="badge bg-success">{{ $order->toStation->name ?? __('unknown') }}</span>
                                            </div>
                                            <small class="text-muted">{{ __('type') }}:
                                                {{ $order->type->name ?? __('unknown') }}</small>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">{{ __('price') }}</label>
                                            <div class="fw-bold fs-5">{{ number_format($order->price, 2) }}
                                                {{ __('currency_unit') }}</div>
                                            @if ($order->points_used > 0)
                                                <small class="text-info">{{ __('points_used') }}:
                                                    {{ $order->points_used }}</small>
                                            @endif
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">{{ __('created_at') }}</label>
                                            <div>{{ $order->created_at->format('Y-m-d H:i') }}</div>
                                        </div>
                                    </div>
                                </div>
                                @if ($order->note)
                                    <div class="mt-3">
                                        <label class="form-label text-muted">{{ __('note') }}</label>
                                        <div class="p-3 bg-light rounded">{{ $order->note }}</div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Order Progress -->
                        {{-- <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('order_progress') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="progress mb-3" style="height: 8px;">
                                    <div class="progress-bar bg-{{ $this->orderProgress['color'] }}"
                                        style="width: {{ $this->orderProgress['percentage'] }}%"></div>
                                </div>

                                <div class="timeline">
                                    @foreach ($this->timelineSteps as $step)
                                        <div
                                            class="timeline-item {{ $step['completed'] ? 'completed' : '' }} {{ $step['active'] ? 'active' : '' }} {{ isset($step['canceled']) ? 'canceled' : '' }}">
                                            <div class="timeline-marker">
                                                <i class="{{ $step['status']->icon() }}"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">{{ $step['title'] }}</h6>
                                                <p class="timeline-description">{{ $step['description'] }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div> --}}

                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Driver Info -->
                        @if ($order->orderDriver)
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('driver_info') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-text avatar-lg bg-primary me-3">
                                            <i class="feather-truck text-white"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">
                                                {{ $order->orderDriver->driver->name ?? __('unknown') }}</div>
                                            <small
                                                class="text-muted">{{ $order->orderDriver->driver->phone ?? '' }}</small>
                                            @if ($order->orderDriver->assigned_at)
                                                <div class="text-info small">
                                                    {{ __('assigned_at') }}:
                                                    {{ $order->orderDriver->assigned_at->format('Y-m-d H:i') }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Quick Actions -->
                        @if (count($allowedStatuses) > 0 && auth()->user()->can('orders.edit'))
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('quick_actions') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex gap-2 flex-wrap">
                                        @foreach ($allowedStatuses as $statusValue => $statusLabel)
                                            @php
                                                $statusEnum = \App\Enums\OrderStatus::from($statusValue);
                                            @endphp
                                            <button type="button" class="btn btn-outline-{{ $statusEnum->color() }}"
                                                wire:click="quickStatusUpdate('{{ $statusValue }}')"
                                                wire:confirm="{{ __('are_you_sure_update_status') }}">
                                                <i class="{{ $statusEnum->icon() }} me-1"></i>
                                                {{ $statusLabel }}
                                            </button>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!-- Tracking History -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('tracking_history') }}</h5>
                            </div>
                            <div class="card-body">
                                @if ($trackings->count() > 0)
                                    <div class="timeline-vertical">
                                        @foreach ($trackings as $tracking)
                                            <div class="timeline-item-vertical">
                                                <div class="timeline-marker-vertical">
                                                    @php
                                                        $trackingStatus = \App\Enums\OrderStatus::from(
                                                            $tracking->status,
                                                        );
                                                    @endphp
                                                    <i
                                                        class="{{ $trackingStatus->icon() }} text-{{ $trackingStatus->color() }}"></i>
                                                </div>
                                                <div class="timeline-content-vertical">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <h6 class="mb-1">{{ $trackingStatus->label() }}</h6>
                                                            @if ($tracking->note)
                                                                <p class="text-muted small mb-1">{{ $tracking->note }}
                                                                </p>
                                                            @endif
                                                            <small class="text-muted">
                                                                {{ __('by') }}:
                                                                {{ $tracking->user->name ?? __('system') }}
                                                            </small>
                                                        </div>
                                                        <small class="text-muted">
                                                            {{ $tracking->created_at->format('M d, H:i') }}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-3">
                                        <div class="text-muted">{{ __('no_tracking_history') }}</div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Status Update Modal -->
                        @if ($showStatusModal)
                            <div class="modal fade show d-block" tabindex="-1"
                                style="background-color: rgba(0,0,0,0.5);">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">{{ __('update_order_status') }}</h5>
                                            <button type="button" class="btn-close"
                                                wire:click="closeStatusModal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">{{ __('current_status') }}</label>
                                                <div>
                                                    <span class="badge bg-{{ $this->statusBadge['color'] }}">
                                                        <i class="{{ $this->statusBadge['icon'] }} me-1"></i>
                                                        {{ $this->statusBadge['label'] }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">{{ __('new_status') }}</label>
                                                <select class="form-select" wire:model="newStatus">
                                                    <option value="">{{ __('select_status') }}</option>
                                                    @foreach ($allowedStatuses as $value => $label)
                                                        <option value="{{ $value }}">{{ $label }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">{{ __('note') }}
                                                    ({{ __('optional') }})</label>
                                                <textarea class="form-control" wire:model="statusNote" rows="3"
                                                    placeholder="{{ __('add_note_for_status_change') }}"></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary"
                                                wire:click="closeStatusModal">
                                                {{ __('cancel') }}
                                            </button>
                                            <button type="button" class="btn btn-primary" wire:click="updateStatus">
                                                {{ __('update_status') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

@push('css')
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 20px;
        }

        .timeline-item:not(:last-child)::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 30px;
            width: 2px;
            height: calc(100% - 10px);
            background-color: #e9ecef;
        }

        .timeline-item.completed:not(:last-child)::before {
            background-color: #28a745;
        }

        .timeline-marker {
            position: absolute;
            left: -30px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
        }

        .timeline-item.completed .timeline-marker {
            background-color: #28a745;
            color: white;
        }

        .timeline-item.active .timeline-marker {
            background-color: #007bff;
            color: white;
        }

        .timeline-item.canceled .timeline-marker {
            background-color: #dc3545;
            color: white;
        }

        .timeline-vertical {
            position: relative;
        }

        .timeline-item-vertical {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px;
        }

        .timeline-item-vertical:not(:last-child)::before {
            content: '';
            position: absolute;
            left: 12px;
            top: 25px;
            width: 2px;
            height: calc(100% - 5px);
            background-color: #e9ecef;
        }

        .timeline-marker-vertical {
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
    </style>
@endpush
