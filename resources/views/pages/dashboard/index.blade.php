<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('dashboard') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('dashboard') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <button type="button" class="btn btn-primary" wire:click="refreshStats">
                                <i class="feather-refresh-cw me-2"></i>
                                {{ __('refresh') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <!-- Main Statistics -->
                    <div class="col-lg-12 mb-4">
                        <div class="row">
                            <!-- Total Orders -->
                            <div class="col-lg-4 col-md-6 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-primary-subtle">
                                                <i class="feather-package text-primary"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['total_orders'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('total_orders') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivered Orders -->
                            <div class="col-lg-4 col-md-6 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-success-subtle">
                                                <i class="feather-check-square text-success"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['delivered_orders'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('delivered') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Stuck Orders -->
                            <div class="col-lg-4 col-md-6 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-danger-subtle">
                                                <i class="feather-alert-triangle text-danger"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['stuck_orders'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('stuck_orders') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Revenue -->
                            {{-- <div class="col-lg-3 col-md-6 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-warning-subtle">
                                                <i class="feather-dollar-sign text-warning"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['total_revenue'] ?? 0, 2) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('total_revenue') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>

                    <!-- Secondary Statistics -->
                    <div class="col-lg-12 mb-4">
                        <div class="row">
                            <!-- Today Orders -->
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-info-subtle">
                                                <i class="feather-calendar text-info"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['today_orders'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('today_orders') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pending Orders -->
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-warning-subtle">
                                                <i class="feather-clock text-warning"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['pending_orders'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('pending') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Users -->
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-secondary-subtle">
                                                <i class="feather-users text-secondary"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['total_users'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('total_users') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Stations -->
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-dark-subtle">
                                                <i class="feather-map-pin text-dark"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['total_stations'] ?? 0) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('total_stations') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- <!-- Delivery Rate -->
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-success-subtle">
                                                <i class="feather-trending-up text-success"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['delivery_rate'] ?? 0, 1) }}%</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('delivery_rate') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Today Revenue -->
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <div class="card stretch stretch-full">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="avatar-text avatar-lg bg-warning-subtle">
                                                <i class="feather-dollar-sign text-warning"></i>
                                            </div>
                                            <div class="text-end">
                                                <h2 class="fs-3 fw-bold text-dark">
                                                    {{ number_format($dashboardStats['today_revenue'] ?? 0, 2) }}</h2>
                                                <p class="fs-12 fw-medium text-muted mb-0">{{ __('today_revenue') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>

                    <!-- Recent Orders and Stuck Orders -->
                    <div class="col-lg-8">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('recent_orders') }}</h5>
                                <div class="card-header-action">
                                    <a href="{{ route('orders.index') }}" class="btn btn-sm btn-primary">
                                        {{ __('view_all') }}
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                @if($recentOrders->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('barcode') }}</th>
                                                    <th>{{ __('user') }}</th>
                                                    <th>{{ __('route') }}</th>
                                                    <th>{{ __('status') }}</th>
                                                    <th>{{ __('created_at') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($recentOrders as $order)
                                                    <tr>
                                                        <td>
                                                            <span class="badge bg-primary">{{ $order->barcode }}</span>
                                                        </td>
                                                        <td>{{ $order->user->name ?? __('unknown') }}</td>
                                                        <td>
                                                            <small>{{ $order->fromStation->name ?? __('unknown') }} → {{ $order->toStation->name ?? __('unknown') }}</small>
                                                        </td>
                                                        <td>
                                                            @php
                                                                $status = \App\Enums\OrderStatus::from($order->status);
                                                            @endphp
                                                            <span class="badge bg-{{ $status->color() }}">
                                                                {{ $status->label() }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <small>{{ $order->created_at->diffForHumans() }}</small>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="feather-package fs-1 text-muted"></i>
                                        <p class="text-muted">{{ __('no_recent_orders') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Stuck Orders -->
                    <div class="col-lg-4">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('stuck_orders') }}</h5>
                                <div class="card-header-action">
                                    <a href="{{ route('orders.index') }}?tab=stuck" class="btn btn-sm btn-danger">
                                        {{ __('view_all') }}
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                @if(count($stuckOrders) > 0)
                                    <div class="list-group list-group-flush">
                                        @foreach($stuckOrders as $order)
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ $order->barcode }}</h6>
                                                    <small class="text-muted">{{ $order->user->name ?? __('unknown') }}</small>
                                                    <br>
                                                    <small class="text-danger">{{ $order->created_at->diffForHumans() }}</small>
                                                </div>
                                                <span class="badge bg-danger">
                                                    {{ \App\Enums\OrderStatus::from($order->status)->label() }}
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="feather-check-circle fs-1 text-success"></i>
                                        <p class="text-muted">{{ __('no_stuck_orders') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>
