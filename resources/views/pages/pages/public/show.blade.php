<div>
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="mb-3">{{ $page->title }}</h1>
                    @if($page->excerpt)
                    <p class="lead mb-3">{{ $page->excerpt }}</p>
                    @endif
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('dashboard') }}">
                                    <i class="feather-home me-1"></i>
                                    {{ __('home') }}
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                {{ $page->title }}
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-4 text-lg-end">
                    @can('pages.edit')
                    <a href="{{ route('pages.edit', $page->id) }}" class="btn btn-light">
                        <i class="feather-edit me-2"></i>
                        {{ __('edit_page') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="card fade-in-up">
                    <div class="card-body">
                        <!-- Page Content -->
                        <div class="page-content">
                            {!! $page->content !!}
                        </div>

                        <!-- Page Footer Info -->
                        <div class="mt-5 pt-4 border-top border-2">
                            <div class="row text-muted">
                                <div class="col-md-6">
                                    @if($page->published_at)
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="feather-calendar me-2 text-primary"></i>
                                        <span>{{ __('published_on') }}: {{ $page->published_at->format('d/m/Y') }}</span>
                                    </div>
                                    @endif
                                    @if($page->updated_at && $page->updated_at != $page->created_at)
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="feather-clock me-2 text-info"></i>
                                        <span>{{ __('last_updated') }}: {{ $page->updated_at->format('d/m/Y H:i') }}</span>
                                    </div>
                                    @endif
                                </div>
                                <div class="col-md-6 text-md-end">
                                    @if($page->createdBy)
                                    <div class="d-flex align-items-center justify-content-md-end mb-2">
                                        <i class="feather-user me-2 text-success"></i>
                                        <span>{{ __('created_by') }}: {{ $page->createdBy->name }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Pages or Actions -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <a href="{{ route('page.show', 'about-us') }}" class="btn btn-outline-primary w-100 mb-3">
                            <i class="feather-info me-2"></i>
                            {{ __('about_us') }}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ route('page.show', 'contact-us') }}" class="btn btn-outline-primary w-100 mb-3">
                            <i class="feather-mail me-2"></i>
                            {{ __('contact_us') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.page-content {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #374151;
}

.page-content h1,
.page-content h2,
.page-content h3,
.page-content h4,
.page-content h5,
.page-content h6 {
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    position: relative;
}

.page-content h1 {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-bottom: 3px solid #e5e7eb;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.page-content h2 {
    font-size: 2rem;
    color: #374151;
    position: relative;
    padding-left: 1rem;
}

.page-content h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.page-content h3 {
    font-size: 1.5rem;
    color: #4b5563;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.page-content p {
    margin-bottom: 1.5rem;
    text-align: justify;
    line-height: 1.8;
}

.page-content ul,
.page-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.page-content li {
    margin-bottom: 0.75rem;
    position: relative;
}

.page-content ul li::marker {
    color: #667eea;
}

.page-content ol li::marker {
    color: #667eea;
    font-weight: 600;
}

.page-content blockquote {
    border-left: 4px solid #667eea;
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 0.75rem;
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-content blockquote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: #667eea;
    opacity: 0.3;
    font-family: serif;
}

.page-content img {
    max-width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
    transition: all 0.3s ease;
}

.page-content img:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.page-content table {
    width: 100%;
    margin-bottom: 2rem;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-content table th,
.page-content table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.page-content table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.page-content table tbody tr:hover {
    background-color: #f8fafc;
}

.page-content table tbody tr:last-child td {
    border-bottom: none;
}

.page-content code {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #7c3aed;
    font-weight: 500;
    border: 1px solid #e2e8f0;
}

.page-content pre {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 0.75rem;
    overflow-x: auto;
    margin: 2rem 0;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.page-content pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-content pre code {
    background: transparent;
    color: inherit;
    padding: 0;
    border: none;
    font-size: 0.875rem;
}

/* Accordion styles for FAQ */
.page-content .accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 0.75rem !important;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-content .accordion-item h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    margin: 0;
    font-size: 1.125rem;
    border-radius: 0;
    border-bottom: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-content .accordion-item h3:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.page-content .accordion-item p {
    padding: 1.5rem;
    margin: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
}

/* Contact info styling */
.page-content .row .col-md-6 h4 {
    color: #667eea;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.page-content .row .col-md-6 p {
    margin-bottom: 0.5rem;
}

.page-content .row .col-md-6 p strong {
    color: #374151;
    font-weight: 600;
}
</style>
@endpush
