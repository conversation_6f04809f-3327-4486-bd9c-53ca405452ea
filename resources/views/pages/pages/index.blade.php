<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('pages') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('pages') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="{{ route('pages.create') }}" class="btn btn-primary">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_page') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <!-- Filters -->
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" placeholder="{{ __('search_pages') }}" wire:model.live="search">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" wire:model.live="statusFilter">
                                            <option value="all">{{ __('all_statuses') }}</option>
                                            @foreach($statusOptions as $key => $label)
                                                <option value="{{ $key }}">{{ $label }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" wire:model.live="showDeleted" id="showDeleted">
                                            <label class="form-check-label" for="showDeleted">
                                                {{ __('show_deleted') }}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <!-- Status counts -->
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-primary">{{ __('all') }}: {{ $statusCounts['all'] }}</span>
                                            <span class="badge bg-success">{{ __('published') }}: {{ $statusCounts['published'] }}</span>
                                            <span class="badge bg-warning">{{ __('draft') }}: {{ $statusCounts['draft'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('pages_list') }}</h5>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0">
                                        <thead>
                                            <tr>
                                                <th>{{ __('title') }}</th>
                                                <th>{{ __('slug') }}</th>
                                                <th>{{ __('status') }}</th>
                                                <th>{{ __('featured') }}</th>
                                                <th>{{ __('published_at') }}</th>
                                                <th>{{ __('created_by') }}</th>
                                                <th>{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($pages as $page)
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <strong>{{ $page->title }}</strong>
                                                            @if($page->excerpt)
                                                                <br><small class="text-muted">{{ Str::limit($page->excerpt, 50) }}</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <code>{{ $page->slug }}</code>
                                                    </td>
                                                    <td>
                                                        @if($page->status === 'published')
                                                            <span class="badge bg-success">{{ __('published') }}</span>
                                                        @elseif($page->status === 'draft')
                                                            <span class="badge bg-warning">{{ __('draft') }}</span>
                                                        @else
                                                            <span class="badge bg-secondary">{{ __('archived') }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($page->is_featured)
                                                            <span class="badge bg-primary">{{ __('featured') }}</span>
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($page->published_at)
                                                            {{ $page->published_at->format('Y-m-d H:i') }}
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($page->creator)
                                                            {{ $page->creator->name }}
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="hstack gap-2 justify-content-end">
                                                            @if($showDeleted)
                                                                @can('pages.edit')
                                                                    <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="restore({{ $page->id }})">
                                                                        <i class="feather-refresh-cw"></i>
                                                                    </a>
                                                                @endcan
                                                            @else
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="show({{ $page->id }})">
                                                                    <i class="feather-eye"></i>
                                                                </a>
                                                                @can('pages.edit')
                                                                    <a href="{{ route('pages.edit', $page->id) }}" class="avatar-text avatar-md">
                                                                        <i class="feather-edit-3"></i>
                                                                    </a>
                                                                    <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="toggleFeatured({{ $page->id }})">
                                                                        <i class="feather-star {{ $page->is_featured ? 'text-warning' : '' }}"></i>
                                                                    </a>
                                                                @endcan
                                                                @can('pages.delete')
                                                                    <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="confirmDelete({{ $page->id }})">
                                                                        <i class="feather-trash-2"></i>
                                                                    </a>
                                                                @endcan
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-file-text fs-1 mb-3"></i>
                                                            <p>{{ __('no_pages_found') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if($pages->hasPages())
                                <div class="card-footer">
                                    {{ $pages->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- View Modal -->
    @if($showViewModal && $selectedPageId)
        @php $page = $this->pageService->findById($selectedPageId) @endphp
        <div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('view_page') }}</h5>
                        <button type="button" class="btn-close" wire:click="closeModal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{ __('title') }}:</strong>
                                <p>{{ $page->title }}</p>
                            </div>
                            <div class="col-md-6">
                                <strong>{{ __('slug') }}:</strong>
                                <p><code>{{ $page->slug }}</code></p>
                            </div>
                            <div class="col-md-6">
                                <strong>{{ __('status') }}:</strong>
                                <p>
                                    @if($page->status === 'published')
                                        <span class="badge bg-success">{{ __('published') }}</span>
                                    @elseif($page->status === 'draft')
                                        <span class="badge bg-warning">{{ __('draft') }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ __('archived') }}</span>
                                    @endif
                                </p>
                            </div>
                            <div class="col-md-6">
                                <strong>{{ __('featured') }}:</strong>
                                <p>
                                    @if($page->is_featured)
                                        <span class="badge bg-primary">{{ __('yes') }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ __('no') }}</span>
                                    @endif
                                </p>
                            </div>
                            <div class="col-12">
                                <strong>{{ __('content') }}:</strong>
                                <div class="border p-3 mt-2" style="max-height: 300px; overflow-y: auto;">
                                    {!! $page->content !!}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeModal">{{ __('close') }}</button>
                        @can('pages.edit')
                            <a href="{{ route('pages.edit', $page->id) }}" class="btn btn-primary">{{ __('edit') }}</a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Delete Modal -->
    @if($showDeleteModal)
        <div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('delete_page') }}</h5>
                        <button type="button" class="btn-close" wire:click="closeModal"></button>
                    </div>
                    <div class="modal-body">
                        <p>{{ __('are_you_sure_delete_page') }}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeModal">{{ __('cancel') }}</button>
                        <button type="button" class="btn btn-danger" wire:click="delete">{{ __('delete') }}</button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
