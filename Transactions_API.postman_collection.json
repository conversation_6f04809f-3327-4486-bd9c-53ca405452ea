{"info": {"_postman_id": "transactions-api-collection", "name": "Transactions API", "description": "API endpoints for managing user transaction history", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/transactions?type=&per_page=15&search=&date_from=&date_to=", "host": ["{{base_url}}"], "path": ["api", "transactions"], "query": [{"key": "type", "value": "", "description": "earned or spent"}, {"key": "per_page", "value": "15", "description": "Number of items per page (1-100)"}, {"key": "search", "value": "", "description": "Search in description or order reference"}, {"key": "date_from", "value": "", "description": "Start date (Y-m-d format)"}, {"key": "date_to", "value": "", "description": "End date (Y-m-d format)"}]}}, "response": []}, {"name": "Get Earned Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/transactions/earned?per_page=10", "host": ["{{base_url}}"], "path": ["api", "transactions", "earned"], "query": [{"key": "per_page", "value": "10"}]}}, "response": []}, {"name": "Get Spent Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/transactions/spent?per_page=10", "host": ["{{base_url}}"], "path": ["api", "transactions", "spent"], "query": [{"key": "per_page", "value": "10"}]}}, "response": []}, {"name": "Get Transaction Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/transactions/1", "host": ["{{base_url}}"], "path": ["api", "transactions", "1"]}}, "response": []}, {"name": "Get Transaction Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/transactions/statistics", "host": ["{{base_url}}"], "path": ["api", "transactions", "statistics"]}}, "response": []}, {"name": "<PERSON>gin (Get Token)", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"123\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "token", "value": "", "type": "string"}]}