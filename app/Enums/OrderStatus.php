<?php

namespace App\Enums;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case CANCELED_BEFORE_TIMED_OUT_BY_USER = 'canceled_before_timed_out_by_user';
    case CANCELED_BEFORE_TIMED_OUT_BY_ADMIN = 'canceled_before_timed_out_by_admin';
    case CANCELED_TIMED_OUT_BY_SYSTEM = 'canceled_timed_out_by_system';
    case CONFIRMED = 'confirmed';
    case CANCELED_AFTER_CONFIRMED_BY_ADMIN = 'canceled_after_confirmed_by_admin';
    case PICKED_UP = 'picked_up';
    case CANCELED_AFTER_PICKED_UP_BY_ADMIN = 'canceled_after_picked_up_by_admin';
    case DELIVERED = 'delivered';
    case CANCELED_AFTER_DELIVERED_BY_ADMIN = 'canceled_after_delivered_by_admin';

    /**
     * Get all status values as array.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get status label for display.
     */
    public function label(): string
    {
        return match ($this) {
            self::PENDING => __('pending'),
            self::CANCELED_BEFORE_TIMED_OUT_BY_USER => __('canceled_before_timed_out_by_user'),
            self::CANCELED_BEFORE_TIMED_OUT_BY_ADMIN => __('canceled_before_timed_out_by_admin'),
            self::CANCELED_TIMED_OUT_BY_SYSTEM => __('canceled_timed_out_by_system'),
            self::CONFIRMED => __('confirmed'),
            self::CANCELED_AFTER_CONFIRMED_BY_ADMIN => __('canceled_after_confirmed_by_admin'),
            self::PICKED_UP => __('picked_up'),
            self::CANCELED_AFTER_PICKED_UP_BY_ADMIN => __('canceled_after_picked_up_by_admin'),
            self::DELIVERED => __('delivered'),
            self::CANCELED_AFTER_DELIVERED_BY_ADMIN => __('canceled_after_delivered_by_admin'),
        };
    }

    /**
     * Get status color for UI.
     */
    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::CONFIRMED => 'info',
            self::PICKED_UP => 'primary',
            self::DELIVERED => 'success',
            default => 'danger', // All canceled statuses
        };
    }

    /**
     * Get status icon.
     */
    public function icon(): string
    {
        return match ($this) {
            self::PENDING => 'feather-clock',
            self::CONFIRMED => 'feather-check-circle',
            self::PICKED_UP => 'feather-package',
            self::DELIVERED => 'feather-check-square',
            default => 'feather-x-circle', // All canceled statuses
        };
    }

    /**
     * Check if status is canceled.
     */
    public function isCanceled(): bool
    {
        return in_array($this, [
            self::CANCELED_BEFORE_TIMED_OUT_BY_USER,
            self::CANCELED_BEFORE_TIMED_OUT_BY_ADMIN,
            self::CANCELED_TIMED_OUT_BY_SYSTEM,
            self::CANCELED_AFTER_CONFIRMED_BY_ADMIN,
            self::CANCELED_AFTER_PICKED_UP_BY_ADMIN,
            self::CANCELED_AFTER_DELIVERED_BY_ADMIN,
        ]);
    }

    /**
     * Check if status is active (not canceled).
     */
    public function isActive(): bool
    {
        return !$this->isCanceled();
    }

    /**
     * Get allowed next statuses for admin.
     */
    public function getAllowedNextStatuses(): array
    {
        return match ($this) {
            self::PENDING => [
                self::CONFIRMED,
                self::CANCELED_BEFORE_TIMED_OUT_BY_ADMIN,
                self::CANCELED_TIMED_OUT_BY_SYSTEM,
            ],
            self::CONFIRMED => [
                self::PICKED_UP,
                self::CANCELED_AFTER_CONFIRMED_BY_ADMIN,
            ],
            self::PICKED_UP => [
                self::DELIVERED,
                self::CANCELED_AFTER_PICKED_UP_BY_ADMIN,
            ],
            self::DELIVERED => [
                self::CANCELED_AFTER_DELIVERED_BY_ADMIN,
            ],
            default => [], // Canceled statuses cannot be changed
        };
    }

    /**
     * Get status options for select dropdown.
     */
    public static function getSelectOptions(): array
    {
        $options = [];
        foreach (self::cases() as $status) {
            $options[$status->value] = $status->label();
        }

        return $options;
    }

    /**
     * Get active status options (non-canceled).
     */
    public static function getActiveStatusOptions(): array
    {
        $options = [];
        foreach (self::cases() as $status) {
            if ($status->isActive()) {
                $options[$status->value] = $status->label();
            }
        }

        return $options;
    }

    /**
     * Check if order is stuck (older than 48 hours and not delivered)
     */
    public static function isOrderStuck(\App\Models\Order $order): bool
    {
        // If order is delivered, it's not stuck
        if ($order->status === self::DELIVERED->value) {
            return false;
        }

        // Check if order is older than 48 hours
        return $order->created_at->diffInHours(now()) > 48;
    }

    /**
     * Get stuck orders statuses (all except delivered)
     */
    public static function getStuckOrderStatuses(): array
    {
        return array_filter(
            array_map(fn($case) => $case->value, self::cases()),
            fn($status) => $status !== self::DELIVERED->value
        );
    }
}
