<?php

namespace App\Console\Commands;

use App\Services\OrderService;
use Illuminate\Console\Command;

class CancelExpiredOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:cancel-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel orders that have exceeded their delivery deadline';

    protected OrderService $orderService;

    /**
     * Create a new command instance.
     */
    public function __construct(OrderService $orderService)
    {
        parent::__construct();
        $this->orderService = $orderService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('بدء فحص الطلبات والاهتمامات المنتهية الصلاحية...');

        try {
            // إلغاء الطلبات المنتهية الصلاحية
            // $canceledOrdersCount = $this->orderService->cancelExpiredOrders();

            // حذف اهتمامات السائقين المنتهية الصلاحية
            $deletedInterestsCount = $this->orderService->cleanExpiredDriverInterests();

            if ($canceledOrdersCount > 0) {
                $this->info("تم إلغاء {$canceledOrdersCount} طلب منتهي الصلاحية");
            }

            if ($deletedInterestsCount > 0) {
                $this->info("تم حذف {$deletedInterestsCount} اهتمام سائق منتهي الصلاحية");
            }

            if ($canceledOrdersCount === 0 && $deletedInterestsCount === 0) {
                $this->info('لا توجد طلبات أو اهتمامات منتهية الصلاحية');
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('حدث خطأ أثناء معالجة الطلبات المنتهية الصلاحية: '.$e->getMessage());

            return 1;
        }
    }
}
