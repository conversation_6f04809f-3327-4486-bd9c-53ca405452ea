<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserRegistration;
use Carbon\Carbon;

class CleanExpiredRegistrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'registrations:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean expired user registrations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expiredCount = UserRegistration::where('code_expires_at', '<', Carbon::now())->count();
        
        UserRegistration::where('code_expires_at', '<', Carbon::now())->delete();
        
        $this->info("تم حذف {$expiredCount} تسجيل منتهي الصلاحية");
        
        return 0;
    }
}
