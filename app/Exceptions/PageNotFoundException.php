<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PageNotFoundException extends Exception
{
    protected $message = 'Page not found or not available';
    protected $code = 404;

    public function __construct(string $message = null, int $code = 404)
    {
        $this->message = $message ?? __('page_not_found_or_disabled');
        $this->code = $code;
        parent::__construct($this->message, $this->code);
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): JsonResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => $this->message,
                'error' => 'page_not_found',
                'code' => $this->code
            ], $this->code);
        }

        // For web requests, redirect to 404 page
        abort(404, $this->message);
    }
}
