<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class LanguageController extends Controller
{
    public function switch($locale)
    {
        $availableLocales = \App\Models\Language::where('is_active', true)->pluck('code')->toArray();
        
        if (in_array($locale, $availableLocales)) {
            session(['locale' => $locale]);
        }
        
        return redirect()->back();
    }

    public function setDefault($code)
    {
        // تحديث ملف .env
        $envPath = base_path('.env');
        $envContent = file_get_contents($envPath);
        
        if (strpos($envContent, 'APP_LOCALE=') !== false) {
            $envContent = preg_replace('/APP_LOCALE=.*/', "APP_LOCALE={$code}", $envContent);
        } else {
            $envContent .= "\nAPP_LOCALE={$code}\n";
        }
        
        file_put_contents($envPath, $envContent);
        
        // تحديث cache
        \Artisan::call('config:clear');
        
        return response()->json(['success' => true, 'message' => __('default_language_updated')]);
    }
}