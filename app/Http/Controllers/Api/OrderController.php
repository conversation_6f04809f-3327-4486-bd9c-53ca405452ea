<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\CreateOrderRequest;
use App\Http\Requests\Api\CancelOrderRequest;
use App\Http\Requests\Api\UpdateOrderStatusRequest;
use App\Http\Resources\OrderResource;
use App\Services\OrderService;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    protected OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function index(Request $request)
    {
        $filters = $request->only(['from_station_id', 'to_station_id', 'type_id', 'status']);
        $perPage = $request->get('per_page', 10);
        $userId = auth()->id();

        $createdOrders = $this->orderService->getCreatedOrders($userId, $filters, $perPage);
        $receivedItems = $this->orderService->getReceivedOrders($userId, $filters, $perPage);

        // تحويل العناصر المختلطة
        $receivedFormatted = $receivedItems->map(function ($item) {
            if ($item instanceof \App\Models\Order) {
                return new OrderResource($item);
            } else {
                return new \App\Http\Resources\OrderDriverResource($item);
            }
        });

        return response()->json([
            'success' => true,
            'message' => 'Orders retrieved successfully',
            'data' => [
                'created' => OrderResource::collection($createdOrders),
                'received' => $receivedFormatted->values(),
            ],
        ]);
    }

    public function store(CreateOrderRequest $request)
    {
        $order = $this->orderService->create($request->validated());
        $order = $order->fresh(['fromStation', 'toStation', 'type', 'latestTracking.user']);

        return response()->json([
            'success' => true,
            'message' => 'Order created successfully',
            'data' => new OrderResource($order),
        ], 201);
    }

    public function show($id)
    {
        $order = $this->orderService->findById($id);

        return response()->json([
            'success' => true,
            'message' => 'Order retrieved successfully',
            'data' => new OrderResource($order),
        ]);
    }

    public function updateStatus(UpdateOrderStatusRequest $request)
    {
        $order = $this->orderService->findById($request->order_id);
        $this->orderService->updateStatus($order, $request->status, $request->note);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully',
            'data' => new OrderResource($order->fresh(['fromStation', 'toStation', 'type', 'latestTracking.user'])),
        ]);
    }

    /**
     * Get user order history with optional status filtering.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function history(Request $request)
    {
        $status = $request->query('status');
        $history = $this->orderService->getUserHistory(auth()->id(), $status);

        return response()->json([
            'success' => true,
            'message' => 'User history retrieved successfully',
            'data' => [
                'created' => OrderResource::collection($history['created']),
                'received' => OrderResource::collection($history['delivered']),
            ],
        ]);
    }

    /**
     * Cancel order by owner before deadline
     */
    public function cancelOrder(CancelOrderRequest $request)
    {
        try {
            $result = $this->orderService->cancelOrderByOwner(
                $request->order_id,
                auth()->id(),
                $request->cancellation_note
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الطلب بنجاح',
                'data' => ['cancelled' => $result],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
