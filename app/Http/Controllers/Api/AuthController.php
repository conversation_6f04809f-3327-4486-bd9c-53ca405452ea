<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AuthResource;
use App\Services\AuthService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    use ApiResponse;
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(Request $request): JsonResponse
    {
        try {
            // تحقق من وجود المستخدم في الجداول
            $existingUser = \App\Models\User::where('phone', $request->phone)->first();
            $existingRegistration = \App\Models\UserRegistration::where('phone', $request->phone)->first();

            // إذا كان المستخدم موجود في الجدول الرئيسي
            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => __('phone_already_registered'),
                ], 409);
            }

            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|string|email|max:255|unique:users,email',
                // 'national_id' => 'required|string|size:10|unique:users,national_id',
                'phone' => 'required|string|max:20|unique:users,phone',
                'password' => 'required|string|min:8',
            ]);

            $result = $this->authService->register($request->all());

            return response()->json([
                'success' => true,
                'data' => $result,
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Registration error: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('registration_error'),
            ], 500);
        }
    }

    public function verifyCode(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string',
                'code' => 'required|string|size:6',
            ]);

            $result = $this->authService->verifyCode($request->phone, $request->code);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error'],
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result,
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('verification_error'),
            ], 500);
        }
    }

    public function login(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string',
                'password' => 'required|string',
            ]);

            $result = $this->authService->login($request->phone, $request->password);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error'],
                ], 401);
            }

            return response()->json([
                'success' => true,
                'data' => $result,
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('login_error'),
            ], 500);
        }
    }

    public function resendCode(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string',
            ]);

            $result = $this->authService->resendCode($request->phone);

            if (isset($result['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error'],
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result,
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('resend_error'),
            ], 500);
        }
    }

    public function logout(Request $request): JsonResponse
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => __('logged_out_successfully'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('logout_error'),
            ], 500);
        }
    }

    public function me(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $request->user(),
        ]);
    }

    /**
     * Update the user's profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'name' => 'sometimes|string|max:255',
                'email' => 'sometimes|string|email|max:255|unique:users,email,'.$request->user()->id,
                'national_id' => 'sometimes|string|max:20|unique:users,national_id,'.$request->user()->id,
                'phone' => 'sometimes|string|max:20|unique:users,phone,'.$request->user()->id,
                'profile_photo' => 'sometimes|image|mimes:jpg,jpeg,png|max:2048', // 2MB max
            ]);

            $result = $this->authService->updateProfile($request->all());

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Profile update error: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('profile_update_error'),
            ], 500);
        }
    }

    /**
     * Delete the user's profile photo.
     */
    public function deleteProfilePhoto(Request $request): JsonResponse
    {
        try {
            $result = $this->authService->deleteProfilePhoto();

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            \Log::error('Profile photo deletion error: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('profile_photo_deletion_error'),
            ], 500);
        }
    }

    /**
     * Send password reset code via SMS.
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string|exists:users,phone',
            ]);

            $result = $this->authService->sendResetCode($request->only('phone'));

            return response()->json([
                'success' => $result['status'],
                'message' => $result['message'],
                'expires_in' => $result['expires_in'] ?? null,
            ], $result['status'] ? 200 : 400);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Forgot password error: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('failed_to_send_reset_code'),
            ], 500);
        }
    }

    /**
     * Verify the password reset code.
     */
    public function verifyResetCode(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string',
                'verification_code' => 'required|string|size:6',
            ]);

            $result = $this->authService->verifyResetCode($request->only(['phone', 'verification_code']));

            if (!$result['status']) {
                return $this->errorResponse($result['message'], 400);
            }

            return $this->successResponse(null, $result['message']);
        } catch (ValidationException $e) {
            return $this->errorResponse(__('validation_error'), 422, $e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse(__('verification_code_error'), 500);
        }
    }

    /**
     * Reset user password.
     *
     * @return JsonResponse
     */
    public function resetPassword(Request $request)
    {
        try {
            $request->validate([
                'phone' => 'required|string|exists:users,phone',
                'password' => 'required|string|min:8', // confirmed,
            ]);

            $result = $this->authService->resetPassword($request->only(
                'phone', 'password'
            ));

            if (!$result['status']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 400);
            }

            $user = $result['user'];
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'data' => new AuthResource($user, $token, $result['message']),
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Password reset error: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('password_reset_error'),
            ], 500);
        }
    }
    // {
    //     try {
    //         $request->validate([
    //             'phone' => 'required|string',
    //             'password' => 'required|string|min:8',
    //         ]);

    //         $result = $this->authService->resetPassword($request->all());

    //         if (!$result['status']) {
    //             return $this->errorResponse($result['message'], 400);
    //         }

    //         return $this->successResponse(null, $result['message']);
    //     } catch (\Exception $e) {
    //         return $this->errorResponse($e->getMessage(), 500);
    //     }
    // }

    /**
     * Reset the user's password using verification code.
     */
    public function resetPasswordOld(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string|exists:users,phone',
                'password' => 'required|string|min:8|confirmed',
            ]);

            $result = $this->authService->resetPassword($request->only(
                'phone', 'password'
            ));

            if (!$result['status']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 400);
            }

            $user = $result['user'];
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'data' => new AuthResource($user, $token, $result['message']),
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Password reset error: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('password_reset_error'),
            ], 500);
        }
    }
}
