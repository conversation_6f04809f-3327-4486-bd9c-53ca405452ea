<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\TypeService;
use App\Http\Resources\TypeResource;
use Illuminate\Http\Request;

class TypeController extends Controller
{
    protected TypeService $typeService;

    public function __construct(TypeService $typeService)
    {
        $this->typeService = $typeService;
    }

    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $types = $this->typeService->getAll($perPage);
        return TypeResource::collection($types);
    }

    public function show($id)
    {
        $type = $this->typeService->findById($id);
        return new TypeResource($type);
    }


}