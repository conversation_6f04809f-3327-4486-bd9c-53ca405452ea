<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\WalletResource;
use App\Http\Resources\PointTransactionResource;
use App\Services\WalletService;
use Illuminate\Http\Request;

class WalletController extends Controller
{
    protected $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    /**
     * Get user's wallet balance and summary
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWallet()
    {
        $wallet = $this->walletService->getUserWallet(auth()->id());
        return new WalletResource($wallet);
    }

    /**
     * Get user's point transactions with pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\Resources\Json\ResourceCollection
     * 
     * @queryParam type string Filter transactions by type. Possible values: earned, spent
     * @queryParam per_page int Items per page. Default: 15
     */
    public function getTransactions(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $type = $request->input('type');
        $transactions = $this->walletService->getUserTransactions(auth()->id(), $perPage, $type);
        return PointTransactionResource::collection($transactions);
    }

    /**
     * Get transaction details with related order
     *
     * @param int $transactionId
     * @return PointTransactionResource
     */
    public function getTransactionDetails($transactionId)
    {
        $transaction = $this->walletService->getTransactionWithOrder(auth()->id(), $transactionId);
        return new PointTransactionResource($transaction);
    }
}
