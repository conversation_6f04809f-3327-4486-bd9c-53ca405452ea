<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\PageNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\StorePageRequest;
use App\Http\Requests\Api\UpdatePageRequest;
use App\Http\Resources\PageResource;
use App\Models\Page;
use App\Services\PageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PageController extends Controller
{
    protected PageService $pageService;

    public function __construct(PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        // Check permission
        if (!auth()->user() || !auth()->user()->can('pages.view')) {
            abort(403, __('unauthorized'));
        }

        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $status = $request->get('status');
        $featured = $request->get('featured');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Use service to get pages with filters
        $pages = $this->pageService->getAll($perPage, false, $search, $status, $featured, $sortBy, $sortOrder);

        return PageResource::collection($pages);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePageRequest $request): PageResource
    {
        // Check permission
        if (!auth()->user() || !auth()->user()->can('pages.create')) {
            abort(403, __('unauthorized'));
        }

        $data = $request->validated();

        // Create the page using the service
        $page = $this->pageService->create($data);

        return new PageResource($page->load(['creator', 'translations']));
    }

    /**
     * Display the specified resource.
     */
    public function show(Page $page): PageResource
    {
        // Check permission
        if (!auth()->user() || !auth()->user()->can('pages.view')) {
            abort(403, __('unauthorized'));
        }

        try {
            // Use service to get page details
            $pageDetails = $this->pageService->findById($page->id);

            if (!$pageDetails) {
                throw new PageNotFoundException(__('page_not_found'));
            }

            return new PageResource($pageDetails);
        } catch (\Exception $e) {
            if ($e instanceof PageNotFoundException) {
                throw $e;
            }
            throw new PageNotFoundException(__('page_not_found'));
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePageRequest $request, Page $page): PageResource
    {
        // Check permission
        if (!auth()->user() || !auth()->user()->can('pages.edit')) {
            abort(403, __('unauthorized'));
        }

        $data = $request->validated();

        // Update the page using the service
        $updatedPage = $this->pageService->update($page->id, $data);

        return new PageResource($updatedPage);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Page $page): JsonResponse
    {
        // Check permission
        if (!auth()->user() || !auth()->user()->can('pages.delete')) {
            abort(403, __('unauthorized'));
        }

        // Use service to delete page
        $this->pageService->delete($page->id);

        return response()->json([
            'message' => __('page_deleted_successfully'),
            'success' => true,
        ]);
    }

    /**
     * Get published pages only (public endpoint).
     */
    public function published(Request $request): AnonymousResourceCollection
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $featured = $request->get('featured');

        // Use service to get published pages
        $pages = $this->pageService->getPublished($perPage, $search, $featured);

        return PageResource::collection($pages);
    }

    /**
     * Get page by slug (public endpoint).
     */
    public function bySlug(string $slug): PageResource
    {
        try {
            // Use service to get page by slug
            $page = $this->pageService->findBySlug($slug);

            if (!$page) {
                throw new PageNotFoundException(__('page_not_found'));
            }

            if ($page->status !== 'published') {
                throw new PageNotFoundException(__('page_not_found_or_disabled'));
            }

            return new PageResource($page);
        } catch (\Exception $e) {
            if ($e instanceof PageNotFoundException) {
                throw $e;
            }
            throw new PageNotFoundException(__('page_not_found'));
        }
    }

    /**
     * Get featured pages (public endpoint).
     */
    public function featured(Request $request): AnonymousResourceCollection
    {
        $limit = $request->get('limit', 5);

        // Use service to get featured pages
        $pages = $this->pageService->getFeatured($limit);

        return PageResource::collection($pages);
    }

    /**
     * Search pages (public endpoint).
     */
    public function search(Request $request): AnonymousResourceCollection
    {
        $query = $request->get('q');
        $perPage = $request->get('per_page', 15);

        if (!$query) {
            return PageResource::collection(collect([]));
        }

        // Use service to search pages
        $pages = $this->pageService->search($query, $perPage, true); // true for published only

        return PageResource::collection($pages);
    }
}
