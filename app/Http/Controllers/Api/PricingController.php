<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PricingResource;
use App\Services\PricingService;
use Illuminate\Http\Request;

class PricingController extends Controller
{
    protected PricingService $pricingService;

    public function __construct(PricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    public function getPrice(Request $request)
    {
        $request->validate([
            'from_station_id' => 'required|exists:stations,id',
            'to_station_id' => 'required|exists:stations,id|different:from_station_id',
            'type_id' => 'required|exists:types,id',
        ]);

        $pricing = $this->pricingService->findByRoute(
            $request->from_station_id,
            $request->to_station_id,
            $request->type_id
        );

        if (!$pricing) {
            return response()->json([
                'success' => false,
                'message' => 'Price not found for this route and type',
                'data' => null
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Price retrieved successfully',
            'data' => new PricingResource($pricing)
        ]);
    }
}