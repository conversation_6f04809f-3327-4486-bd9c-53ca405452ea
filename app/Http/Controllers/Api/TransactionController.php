<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TransactionResource;
use App\Models\PointTransaction;
use App\Services\WalletService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class TransactionController extends Controller
{
    protected $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    /**
     * Get user's transaction history.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'type' => ['nullable', Rule::in(['earned', 'spent'])],
                'per_page' => 'nullable|integer|min:1|max:100',
                'search' => 'nullable|string|max:255',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
            ]);

            $user = auth()->user();

            $transactions = $this->walletService->getUserTransactions(
                $user->id,
                $request->get('per_page', 15),
                $request->get('type'),
                $request->get('search'),
                $request->get('date_from'),
                $request->get('date_to')
            );

            // Get user wallet summary
            $wallet = $this->walletService->getUserWallet($user->id);

            return response()->json([
                'status' => 'success',
                'message' => __('transactions_retrieved_successfully'),
                'data' => [
                    'wallet_summary' => $wallet,
                    'transactions' => [
                        'data' => TransactionResource::collection($transactions->items()),
                        'current_page' => $transactions->currentPage(),
                        'last_page' => $transactions->lastPage(),
                        'per_page' => $transactions->perPage(),
                        'total' => $transactions->total(),
                        'from' => $transactions->firstItem(),
                        'to' => $transactions->lastItem(),
                    ],
                ],
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('validation_error'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('something_went_wrong'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get earned transactions only.
     */
    public function earned(Request $request): JsonResponse
    {
        $request->merge(['type' => 'earned']);

        return $this->index($request);
    }

    /**
     * Get spent transactions only.
     */
    public function spent(Request $request): JsonResponse
    {
        $request->merge(['type' => 'spent']);

        return $this->index($request);
    }

    /**
     * Get transaction details by ID.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $user = auth()->user();

            $transaction = $this->walletService->getTransactionWithOrder($user->id, $id);

            return response()->json([
                'status' => 'success',
                'message' => __('transaction_retrieved_successfully'),
                'data' => new TransactionResource($transaction),
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('transaction_not_found'),
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('something_went_wrong'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get transaction statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $user = auth()->user();
            $wallet = $this->walletService->getUserWallet($user->id);

            // Get monthly statistics for the last 6 months
            $monthlyStats = [];
            for ($i = 5; $i >= 0; --$i) {
                $date = now()->subMonths($i);
                $startOfMonth = $date->copy()->startOfMonth();
                $endOfMonth = $date->copy()->endOfMonth();

                $earned = PointTransaction::where('user_id', $user->id)
                    ->where('type', 'earned')
                    ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                    ->sum('amount');

                $spent = PointTransaction::where('user_id', $user->id)
                    ->where('type', 'spent')
                    ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                    ->sum('amount');

                $monthlyStats[] = [
                    'month' => $date->format('Y-m'),
                    'month_name' => $date->format('F Y'),
                    'earned' => $earned,
                    'spent' => $spent,
                    'net' => $earned - $spent,
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => __('statistics_retrieved_successfully'),
                'data' => [
                    'wallet_summary' => $wallet,
                    'monthly_statistics' => $monthlyStats,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('something_went_wrong'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
