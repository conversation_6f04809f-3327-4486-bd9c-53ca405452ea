<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AttendanceResource;
use App\Services\AttendanceService;
use Illuminate\Http\Request;

class AttendanceController extends Controller
{
    protected AttendanceService $attendanceService;

    public function __construct(AttendanceService $attendanceService)
    {
        $this->attendanceService = $attendanceService;
    }

    public function store(Request $request)
    {
        $request->validate([
            'type_id' => 'required|exists:types,id',
            'from_station_id' => 'required|exists:stations,id',
            'to_station_id' => 'required|exists:stations,id',
        ]);

        $attendance = $this->attendanceService->register(
            auth()->id(),
            $request->type_id,
            $request->from_station_id,
            $request->to_station_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Attendance registered successfully',
            'data' => new AttendanceResource($attendance->load(['type', 'fromStation', 'toStation'])),
        ]);
    }

    public function destroy()
    {
        $deleted = $this->attendanceService->remove(auth()->id());

        if ($deleted) {
            return response()->json([
                'success' => true,
                'message' => 'Attendance removed successfully',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Attendance not found',
        ], 404);
    }
}