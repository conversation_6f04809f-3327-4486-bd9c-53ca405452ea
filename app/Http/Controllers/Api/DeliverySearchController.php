<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\AssignDriverRequest;
use App\Http\Requests\Api\CancelDriverInterestRequest;
use App\Http\Resources\DeliverySearchResource;
use App\Services\OrderService;
use Illuminate\Http\Request;

class DeliverySearchController extends Controller
{
    protected OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function search(Request $request)
    {
        $request->validate([
            'from_station_id' => 'required|exists:stations,id',
            'to_station_id' => 'required|exists:stations,id',
        ]);

        $result = $this->orderService->searchAvailableDeliveries(
            auth()->id(),
            $request->from_station_id,
            $request->to_station_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Available delivery orders retrieved successfully',
            'data' => DeliverySearchResource::collection($result),
        ]);
    }

    public function registerInterest(AssignDriverRequest $request)
    {
        $interest = $this->orderService->registerInterest(
            auth()->id(),
            $request->type_id,
            $request->from_station_id,
            $request->to_station_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Interest registered successfully',
            'data' => ['interest_id' => $interest->id],
        ]);
    }

    public function getRandomOrder(Request $request)
    {
        $request->validate([
            'interest_id' => 'required|exists:order_drivers,id',
        ]);

        try {
            $order = $this->orderService->assignRandomOrder($request->interest_id);

            return response()->json([
                'success' => true,
                'message' => 'Order assigned successfully',
                'data' => new \App\Http\Resources\OrderResource($order),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Cancel driver interest before deadline
     */
    public function cancelInterest(CancelDriverInterestRequest $request)
    {
        try {
            $result = $this->orderService->cancelDriverInterest(
                $request->order_driver_id,
                auth()->id(),
                'cancelled_by_driver',
                $request->cancellation_note
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الاهتمام بنجاح',
                'data' => ['cancelled' => $result],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}