<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StationService;
use App\Http\Resources\StationResource;
use Illuminate\Http\Request;

class StationController extends Controller
{
    protected StationService $stationService;

    public function __construct(StationService $stationService)
    {
        $this->stationService = $stationService;
    }

    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $latitude = $request->get('latitude');
        $longitude = $request->get('longitude');

        // التحقق من صحة اللات واللونج إذا تم تمريرهما
        if (($latitude !== null && $longitude === null) || ($latitude === null && $longitude !== null)) {
            return response()->json([
                'message' => 'Both latitude and longitude are required when filtering by location'
            ], 422);
        }

        if ($latitude !== null && $longitude !== null) {
            // التحقق من صحة القيم
            if (!is_numeric($latitude) || !is_numeric($longitude)) {
                return response()->json([
                    'message' => 'Latitude and longitude must be numeric values'
                ], 422);
            }

            if ($latitude < -90 || $latitude > 90) {
                return response()->json([
                    'message' => 'Latitude must be between -90 and 90'
                ], 422);
            }

            if ($longitude < -180 || $longitude > 180) {
                return response()->json([
                    'message' => 'Longitude must be between -180 and 180'
                ], 422);
            }
        }

        $stations = $this->stationService->getAll($perPage, false, $latitude, $longitude);
        return StationResource::collection($stations);
    }

    public function show($id)
    {
        $station = $this->stationService->findById($id);
        return new StationResource($station);
    }


}
