<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class ApiDocumentationController extends Controller
{
    /**
     * Get Pages API documentation
     *
     * @return JsonResponse
     */
    public function pages(): JsonResponse
    {
        return response()->json([
            'title' => 'Pages API Documentation',
            'version' => '1.0.0',
            'base_url' => url('/api'),
            'authentication' => [
                'type' => 'Bearer Token (Sanctum)',
                'header' => 'Authorization: Bearer {token}',
                'note' => 'Some endpoints are public and don\'t require authentication'
            ],
            'endpoints' => [
                'public' => [
                    [
                        'method' => 'GET',
                        'endpoint' => '/api/pages/published',
                        'description' => 'Get all published pages',
                        'auth_required' => false,
                        'parameters' => [
                            'per_page' => 'integer (optional, default: 15)',
                            'search' => 'string (optional)',
                            'featured' => 'boolean (optional)',
                        ],
                        'response' => [
                            'data' => 'Array of page objects',
                            'links' => 'Pagination links',
                            'meta' => 'Pagination metadata'
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'endpoint' => '/api/pages/featured',
                        'description' => 'Get featured pages',
                        'auth_required' => false,
                        'parameters' => [
                            'limit' => 'integer (optional, default: 5)',
                        ],
                        'response' => [
                            'data' => 'Array of featured page objects'
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'endpoint' => '/api/pages/search',
                        'description' => 'Search in published pages',
                        'auth_required' => false,
                        'parameters' => [
                            'q' => 'string (required) - Search query',
                            'per_page' => 'integer (optional, default: 15)',
                        ],
                        'response' => [
                            'data' => 'Array of matching page objects',
                            'links' => 'Pagination links',
                            'meta' => 'Pagination metadata'
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'endpoint' => '/api/pages/slug/{slug}',
                        'description' => 'Get page by slug',
                        'auth_required' => false,
                        'parameters' => [
                            'slug' => 'string (required) - Page slug'
                        ],
                        'response' => [
                            'data' => 'Page object'
                        ]
                    ]
                ],
                'protected' => [
                    [
                        'method' => 'GET',
                        'endpoint' => '/api/pages',
                        'description' => 'Get all pages (admin)',
                        'auth_required' => true,
                        'permission' => 'pages.view',
                        'parameters' => [
                            'per_page' => 'integer (optional, default: 15)',
                            'search' => 'string (optional)',
                            'status' => 'string (optional) - draft|published|archived',
                            'featured' => 'boolean (optional)',
                            'sort_by' => 'string (optional, default: created_at)',
                            'sort_order' => 'string (optional, default: desc)',
                        ],
                        'response' => [
                            'data' => 'Array of page objects',
                            'links' => 'Pagination links',
                            'meta' => 'Pagination metadata'
                        ]
                    ],
                    [
                        'method' => 'POST',
                        'endpoint' => '/api/pages',
                        'description' => 'Create new page',
                        'auth_required' => true,
                        'permission' => 'pages.create',
                        'body' => [
                            'title' => 'object (required) - {en: "Title", ar: "العنوان"}',
                            'slug' => 'string (required) - Unique slug',
                            'content' => 'object (optional) - {en: "Content", ar: "المحتوى"}',
                            'excerpt' => 'object (optional) - {en: "Excerpt", ar: "المقتطف"}',
                            'featured_image' => 'string (optional)',
                            'status' => 'string (required) - draft|published|archived',
                            'is_featured' => 'boolean (optional)',
                            'sort_order' => 'integer (optional)',
                            'published_at' => 'datetime (optional)',
                            'meta_title' => 'object (optional)',
                            'meta_description' => 'object (optional)',
                            'meta_keywords' => 'object (optional)'
                        ],
                        'response' => [
                            'data' => 'Created page object'
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'endpoint' => '/api/pages/{id}',
                        'description' => 'Get specific page',
                        'auth_required' => true,
                        'permission' => 'pages.view',
                        'parameters' => [
                            'id' => 'integer (required) - Page ID'
                        ],
                        'response' => [
                            'data' => 'Page object'
                        ]
                    ],
                    [
                        'method' => 'PUT/PATCH',
                        'endpoint' => '/api/pages/{id}',
                        'description' => 'Update page',
                        'auth_required' => true,
                        'permission' => 'pages.edit',
                        'parameters' => [
                            'id' => 'integer (required) - Page ID'
                        ],
                        'body' => [
                            'title' => 'object (optional) - {en: "Title", ar: "العنوان"}',
                            'slug' => 'string (optional) - Unique slug',
                            'content' => 'object (optional) - {en: "Content", ar: "المحتوى"}',
                            'excerpt' => 'object (optional) - {en: "Excerpt", ar: "المقتطف"}',
                            'featured_image' => 'string (optional)',
                            'status' => 'string (optional) - draft|published|archived',
                            'is_featured' => 'boolean (optional)',
                            'sort_order' => 'integer (optional)',
                            'published_at' => 'datetime (optional)',
                            'meta_title' => 'object (optional)',
                            'meta_description' => 'object (optional)',
                            'meta_keywords' => 'object (optional)'
                        ],
                        'response' => [
                            'data' => 'Updated page object'
                        ]
                    ],
                    [
                        'method' => 'DELETE',
                        'endpoint' => '/api/pages/{id}',
                        'description' => 'Delete page',
                        'auth_required' => true,
                        'permission' => 'pages.delete',
                        'parameters' => [
                            'id' => 'integer (required) - Page ID'
                        ],
                        'response' => [
                            'message' => 'Success message',
                            'success' => true
                        ]
                    ]
                ]
            ],
            'page_object_structure' => [
                'id' => 'integer',
                'title' => 'string',
                'slug' => 'string',
                'content' => 'string (HTML)',
                'excerpt' => 'string',
                'featured_image' => 'string|null',
                'status' => 'string (draft|published|archived)',
                'is_featured' => 'boolean',
                'sort_order' => 'integer',
                'published_at' => 'datetime|null',
                'created_at' => 'datetime',
                'updated_at' => 'datetime',
                'meta' => [
                    'title' => 'string|null',
                    'description' => 'string|null',
                    'keywords' => 'array|null'
                ],
                'author' => [
                    'id' => 'integer',
                    'name' => 'string',
                    'email' => 'string'
                ],
                'translations' => [
                    'en' => [
                        'title' => 'string',
                        'content' => 'string',
                        'excerpt' => 'string',
                        'meta_title' => 'string',
                        'meta_description' => 'string',
                        'meta_keywords' => 'array'
                    ],
                    'ar' => [
                        'title' => 'string',
                        'content' => 'string',
                        'excerpt' => 'string',
                        'meta_title' => 'string',
                        'meta_description' => 'string',
                        'meta_keywords' => 'array'
                    ]
                ],
                'urls' => [
                    'public' => 'string',
                    'edit' => 'string',
                    'api' => 'string'
                ]
            ],
            'error_responses' => [
                '401' => 'Unauthorized - Invalid or missing token',
                '403' => 'Forbidden - Insufficient permissions',
                '404' => 'Not Found - Resource not found',
                '422' => 'Validation Error - Invalid input data',
                '500' => 'Server Error - Internal server error'
            ]
        ]);
    }
}
