<?php

namespace App\Http\Controllers\Web\Pricings;

use App\Services\PricingService;
use App\Services\StationService;
use App\Services\TypeService;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Create extends Component
{
    protected PricingService $pricingService;
    protected StationService $stationService;
    protected TypeService $typeService;
    
    public $from_station_id, $to_station_id, $prices = [];

    public function boot(PricingService $pricingService, StationService $stationService, TypeService $typeService)
    {
        $this->pricingService = $pricingService;
        $this->stationService = $stationService;
        $this->typeService = $typeService;
    }

    public function store()
    {
        $this->validate([
            'from_station_id' => 'required|exists:stations,id|different:to_station_id',
            'to_station_id' => 'required|exists:stations,id|different:from_station_id',
            'prices.*' => 'nullable|numeric|min:0',
        ], [
            'from_station_id.required' => __('from_station_required'),
            'from_station_id.different' => __('stations_must_be_different'),
            'to_station_id.required' => __('to_station_required'),
            'to_station_id.different' => __('stations_must_be_different'),
            'prices.*.numeric' => __('price_must_be_number'),
            'prices.*.min' => __('price_must_be_positive'),
        ]);

        $existingRoute = $this->pricingService->getRouteExists($this->from_station_id, $this->to_station_id);

        if ($existingRoute) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('route_already_exists'))->confirmButtonText(__('yes'))->show();
            return;
        }

        foreach ($this->prices as $typeId => $price) {
            if ($price > 0) {
                $this->pricingService->create([
                    'from_station_id' => $this->from_station_id,
                    'to_station_id' => $this->to_station_id,
                    'type_id' => $typeId,
                    'price' => $price,
                ]);
            }
        }

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_created_successfully'))->confirmButtonText(__('yes'))->show();
        return redirect()->route('pricings.index');
    }

    public function render()
    {
        return view('pages.pricings.create-page', [
            'stations' => $this->stationService->getAll(1000)->items(),
            'types' => $this->typeService->getAll(1000)->items(),
        ])->layout('layouts.app', [
            'availableLanguages' => \App\Models\Language::all()
        ]);
    }
}