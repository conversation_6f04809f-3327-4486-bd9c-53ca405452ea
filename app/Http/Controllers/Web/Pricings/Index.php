<?php

namespace App\Http\Controllers\Web\Pricings;

use App\Services\PricingService;
use App\Services\StationService;
use App\Services\TypeService;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    protected array $paginationOptions = [
        'bootstrap' => true,
        'align' => 'center',
        'size' => 'default',
    ];

    protected PricingService $pricingService;
    protected StationService $stationService;
    protected TypeService $typeService;

    public $pricing;
    public $from_station_id;
    public $to_station_id;
    public $prices = [];
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;
    public $showRestoreModal = false;
    public $showForceDeleteModal = false;
    public $showViewModal = false;
    public $showDeleted = false;

    public function boot(PricingService $pricingService, StationService $stationService, TypeService $typeService)
    {
        $this->pricingService = $pricingService;
        $this->stationService = $stationService;
        $this->typeService = $typeService;
    }

    public function create()
    {
        return redirect()->route('pricings.create');
    }

    public function store()
    {
        $this->validate([
            'from_station_id' => 'required|exists:stations,id|different:to_station_id',
            'to_station_id' => 'required|exists:stations,id|different:from_station_id',
            'prices.*' => 'nullable|numeric|min:0',
        ], [
            'from_station_id.required' => __('from_station_required'),
            'from_station_id.different' => __('stations_must_be_different'),
            'to_station_id.required' => __('to_station_required'),
            'to_station_id.different' => __('stations_must_be_different'),
            'prices.*.numeric' => __('price_must_be_number'),
            'prices.*.min' => __('price_must_be_positive'),
        ]);

        $existingRoute = $this->pricingService->getRouteExists($this->from_station_id, $this->to_station_id);

        if ($existingRoute) {
            // تعديل الأسعار الموجودة
            foreach ($this->prices as $typeId => $price) {
                if ($price > 0) {
                    $this->pricingService->updateOrCreatePrice(
                        $this->from_station_id,
                        $this->to_station_id,
                        $typeId,
                        $price
                    );
                } else {
                    // حذف السعر إذا كان صفر أو فارغ
                    $this->pricingService->deletePrice(
                        $this->from_station_id,
                        $this->to_station_id,
                        $typeId
                    );
                }
            }
            $message = __('pricing_updated_successfully');
        } else {
            // إنشاء جديد
            foreach ($this->prices as $typeId => $price) {
                if ($price > 0) {
                    $this->pricingService->create([
                        'from_station_id' => $this->from_station_id,
                        'to_station_id' => $this->to_station_id,
                        'type_id' => $typeId,
                        'price' => $price,
                    ]);
                }
            }
            $message = __('pricing_created_successfully');
        }

        $this->showCreateModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text($message)->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        $this->pricing = $this->pricingService->findById($id);
        $this->from_station_id = $this->pricing->from_station_id;
        $this->to_station_id = $this->pricing->to_station_id;
        $this->type_id = $this->pricing->type_id;
        $this->price = $this->pricing->price;
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->validate([
            'from_station_id' => 'required|exists:stations,id|different:to_station_id',
            'to_station_id' => 'required|exists:stations,id|different:from_station_id',
            'type_id' => 'required|exists:types,id',
            'price' => 'required|numeric|min:0',
        ]);

        $this->pricingService->update($this->pricing, [
            'from_station_id' => $this->from_station_id,
            'to_station_id' => $this->to_station_id,
            'type_id' => $this->type_id,
            'price' => $this->price,
        ]);

        $this->showEditModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmDelete($id)
    {
        $this->pricing = $this->pricingService->findById($id);
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        $this->pricingService->delete($this->pricing);
        $this->showDeleteModal = false;
        $this->reset(['pricing']);
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmRestore($id)
    {
        $this->pricing = $this->pricingService->findById($id);
        $this->showRestoreModal = true;
    }

    public function restore()
    {
        $this->pricingService->restore($this->pricing);
        $this->showRestoreModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_restored_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmForceDelete($id)
    {
        $this->pricing = $this->pricingService->findById($id);
        $this->showForceDeleteModal = true;
    }

    public function forceDelete()
    {
        $this->pricingService->forceDelete($this->pricing);
        $this->showForceDeleteModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_permanently_deleted'))->confirmButtonText(__('yes'))->show();
    }

    public function editRoute($fromStationId, $toStationId)
    {
        return redirect()->route('pricings.edit', [$fromStationId, $toStationId]);
    }

    public function confirmDeleteRoute($fromStationId, $toStationId)
    {
        $this->from_station_id = $fromStationId;
        $this->to_station_id = $toStationId;
        $this->showDeleteModal = true;
    }

    public function deleteRoute()
    {
        $this->pricingService->deleteRoute($this->from_station_id, $this->to_station_id);

        $this->showDeleteModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmRestoreRoute($fromStationId, $toStationId)
    {
        $this->from_station_id = $fromStationId;
        $this->to_station_id = $toStationId;
        $this->showRestoreModal = true;
    }

    public function restoreRoute()
    {
        $this->pricingService->restoreRoute($this->from_station_id, $this->to_station_id);

        $this->showRestoreModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_restored_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmForceDeleteRoute($fromStationId, $toStationId)
    {
        $this->from_station_id = $fromStationId;
        $this->to_station_id = $toStationId;
        $this->showForceDeleteModal = true;
    }

    public function forceDeleteRoute()
    {
        $this->pricingService->forceDeleteRoute($this->from_station_id, $this->to_station_id);

        $this->showForceDeleteModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('pricing_permanently_deleted'))->confirmButtonText(__('yes'))->show();
    }

    public function viewRoute($fromStationId, $toStationId)
    {
        return redirect()->route('pricings.show', [$fromStationId, $toStationId]);
    }

    public function getAvailableToStations()
    {
        return $this->stationService->getAll(1000)->items()->filter(function ($station) {
            return $station->id != $this->from_station_id;
        });
    }

    public function getAvailableFromStations()
    {
        return $this->stationService->getAll(1000)->items()->filter(function ($station) {
            return $station->id != $this->to_station_id;
        });
    }

    private function resetForm()
    {
        $this->reset(['from_station_id', 'to_station_id', 'prices', 'pricing']);
        $this->prices = [];
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showDeleteModal = false;
        $this->showRestoreModal = false;
        $this->showForceDeleteModal = false;
        $this->showViewModal = false;
        $this->resetForm();
    }

    public function render()
    {
        return view('pages.pricings.index', [
            'pricings' => $this->pricingService->getAll(10, $this->showDeleted),
            'stations' => $this->stationService->getAll(1000)->items(),
            'types' => $this->typeService->getAll(1000)->items(),
        ])->layout('layouts.app');
    }
}
