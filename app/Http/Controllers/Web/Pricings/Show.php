<?php

namespace App\Http\Controllers\Web\Pricings;

use App\Services\PricingService;
use App\Services\StationService;
use App\Services\TypeService;
use Livewire\Component;

class Show extends Component
{
    protected PricingService $pricingService;
    protected StationService $stationService;
    protected TypeService $typeService;
    
    public $from_station_id, $to_station_id, $prices = [];

    public function boot(PricingService $pricingService, StationService $stationService, TypeService $typeService)
    {
        $this->pricingService = $pricingService;
        $this->stationService = $stationService;
        $this->typeService = $typeService;
    }

    public function mount($fromStationId, $toStationId)
    {
        $this->from_station_id = $fromStationId;
        $this->to_station_id = $toStationId;
        
        $existingPrices = $this->pricingService->getRoutePrices($fromStationId, $toStationId);
        
        $this->prices = [];
        foreach ($existingPrices as $pricing) {
            $this->prices[$pricing->type_id] = $pricing->price;
        }
    }

    public function render()
    {
        return view('pages.pricings.show-page', [
            'stations' => $this->stationService->getAll(1000)->items(),
            'types' => $this->typeService->getAll(1000)->items(),
        ])->layout('layouts.app', [
            'availableLanguages' => \App\Models\Language::all()
        ]);
    }
}