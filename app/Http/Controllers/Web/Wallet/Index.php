<?php

namespace App\Http\Controllers\Web\Wallet;

use App\Models\User;
use App\Services\WalletService;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    protected array $paginationOptions = [
        'bootstrap' => true,
        'align' => 'center',
        'size' => 'default',
    ];
    protected $walletService;
    public $balance = 0;
    public $totalEarned = 0;
    public $totalSpent = 0;
    public $transactionType = '';
    public $perPage = 10;
    public $search = '';
    public $dateFrom = '';
    public $dateTo = '';
    public $selectedUserId = '';
    public $isAdmin = false;

    // Admin stats
    public $totalBalance = 0;
    public $totalTransactions = 0;
    public $totalUsers = 0;

    public function boot(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function mount()
    {
        $this->isAdmin = auth()->user()->hasRole('admin');
        $this->loadWalletData();
    }

    public function loadWalletData()
    {
        if ($this->isAdmin) {
            // Load admin stats
            $stats = $this->walletService->getAdminWalletStats();
            $this->totalBalance = $stats['total_balance'];
            $this->totalEarned = $stats['total_earned'];
            $this->totalSpent = $stats['total_spent'];
            $this->totalTransactions = $stats['total_transactions'];
            $this->totalUsers = $stats['total_users'];
        } else {
            // Load user wallet data
            $wallet = $this->walletService->getUserWallet(auth()->id());
            $this->balance = $wallet['balance'];
            $this->totalEarned = $wallet['total_earned'];
            $this->totalSpent = $wallet['total_spent'];
        }
    }

    public function getTransactionsProperty()
    {
        if ($this->isAdmin) {
            // Admin sees all transactions
            return $this->walletService->getAllTransactions(
                $this->perPage,
                $this->transactionType ?: null,
                $this->search,
                $this->dateFrom,
                $this->dateTo,
                $this->selectedUserId ?: null
            );
        } else {
            // Regular user sees only their transactions
            return $this->walletService->getUserTransactions(
                auth()->id(),
                $this->perPage,
                $this->transactionType ?: null,
                $this->search,
                $this->dateFrom,
                $this->dateTo
            );
        }
    }

    public function getUsersProperty()
    {
        if ($this->isAdmin) {
            return User::whereHas('points')->orderBy('name')->get();
        }

        return collect();
    }

    public function resetFilters()
    {
        $this->reset(['transactionType', 'search', 'dateFrom', 'dateTo', 'selectedUserId']);
        $this->resetPage();
    }

    // Add listeners for live filtering
    public function updatedSelectedUserId()
    {
        $this->resetPage();
    }

    public function updatedTransactionType()
    {
        $this->resetPage();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        return view('pages.wallet.index', [
            'transactions' => $this->transactions,
            'users' => $this->users,
        ]);
    }
}
