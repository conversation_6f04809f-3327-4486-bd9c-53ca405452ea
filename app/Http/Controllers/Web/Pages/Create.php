<?php

namespace App\Http\Controllers\Web\Pages;

use App\Services\PageService;
use Livewire\Component;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class Create extends Component
{

    protected PageService $pageService;

    // Form fields
    public $title = '';
    public $slug = '';
    public $content = '';
    public $excerpt = '';
    public $meta_title = '';
    public $meta_description = '';
    public $meta_keywords = [];
    public $featured_image = '';
    public $status = 'draft';
    public $is_featured = false;
    public $sort_order = 0;
    public $published_at = '';

    // Language management
    public $currentLanguage = 'ar';
    public $availableLanguages = [];

    public function boot(PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    public function mount()
    {
        if (!auth()->user()->can('pages.create')) {
            abort(403, __('unauthorized'));
        }

        $this->availableLanguages = \App\Models\Language::where('is_active', true)->get();
        $this->currentLanguage = app()->getLocale();
    }

    public function render()
    {
        return view('pages.pages.create');
    }

    public function store()
    {
        if (!auth()->user()->can('pages.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'sort_order' => 'integer|min:0',
            'published_at' => 'nullable|date',
        ]);

        $data = [
            'title' => $this->title,
            'slug' => $this->slug,
            'content' => $this->content,
            'excerpt' => $this->excerpt,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'featured_image' => $this->featured_image,
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'sort_order' => $this->sort_order,
            'published_at' => $this->published_at,
        ];

        $page = $this->pageService->create($data);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_created_successfully'))->confirmButtonText(__('yes'))->show();

        return redirect()->route('pages.edit', $page->id);
    }

    public function saveAsDraft()
    {
        $this->status = 'draft';
        $this->store();
    }

    public function saveAndPublish()
    {
        $this->status = 'published';
        if (empty($this->published_at)) {
            $this->published_at = now()->format('Y-m-d\TH:i');
        }
        $this->store();
    }

    public function generateSlug()
    {
        if (!empty($this->title)) {
            $this->slug = \Str::slug($this->title);
        }
    }

    public function switchLanguage($languageCode)
    {
        $this->currentLanguage = $languageCode;

        // Emit event to update editor content
        $this->dispatch('languageSwitched', $this->content);
    }

    public function updatedTitle()
    {
        if (empty($this->slug)) {
            $this->generateSlug();
        }
    }

    public function addKeyword()
    {
        $this->meta_keywords[] = '';
    }

    public function removeKeyword($index)
    {
        unset($this->meta_keywords[$index]);
        $this->meta_keywords = array_values($this->meta_keywords);
    }

    public function preview()
    {
        // يمكن إضافة منطق المعاينة هنا
        (new LivewireAlert($this))->info()->title(__('info'))->text(__('preview_feature_coming_soon'))->confirmButtonText(__('yes'))->show();
    }

    public function cancel()
    {
        return redirect()->route('pages.index');
    }
}
