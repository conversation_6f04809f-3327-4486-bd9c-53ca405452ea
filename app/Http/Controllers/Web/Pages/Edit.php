<?php

namespace App\Http\Controllers\Web\Pages;

use App\Services\PageService;
use App\Models\Page;
use Livewire\Component;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;

class Edit extends Component
{

    protected PageService $pageService;

    public Page $page;

    // Form fields
    public $title = '';
    public $slug = '';
    public $content = '';
    public $excerpt = '';
    public $meta_title = '';
    public $meta_description = '';
    public $meta_keywords = [];
    public $featured_image = '';
    public $status = 'draft';
    public $is_featured = false;
    public $sort_order = 0;
    public $published_at = '';

    // Language management
    public $currentLanguage = 'ar';
    public $availableLanguages = [];

    public function boot(PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    public function mount($id)
    {
        if (!auth()->user()->can('pages.edit')) {
            abort(403, __('unauthorized'));
        }

        $this->page = $this->pageService->findById($id);
        $this->availableLanguages = \App\Models\Language::where('is_active', true)->get();
        $this->currentLanguage = app()->getLocale();

        $this->loadPageData();

        // Dispatch initial content to editor
        $this->dispatch('contentUpdated', $this->content);
    }

    protected function loadPageData()
    {
        // تحميل البيانات المترجمة للغة الحالية
        $this->title = $this->page->getTranslation('title', $this->currentLanguage) ?? '';
        $this->slug = $this->page->slug;
        $this->content = $this->page->getTranslation('content', $this->currentLanguage) ?? '';
        $this->excerpt = $this->page->getTranslation('excerpt', $this->currentLanguage) ?? '';
        $this->meta_title = $this->page->getTranslation('meta_title', $this->currentLanguage) ?? '';
        $this->meta_description = $this->page->getTranslation('meta_description', $this->currentLanguage) ?? '';
        $this->meta_keywords = $this->page->getTranslation('meta_keywords', $this->currentLanguage) ?? [];
        $this->featured_image = $this->page->featured_image;
        $this->status = $this->page->status;
        $this->is_featured = $this->page->is_featured;
        $this->sort_order = $this->page->sort_order;
        $this->published_at = $this->page->published_at?->format('Y-m-d\TH:i') ?? '';
    }

    public function render()
    {
        return view('pages.pages.edit');
    }

    public function update()
    {
        if (!auth()->user()->can('pages.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug,' . $this->page->id,
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'sort_order' => 'integer|min:0',
            'published_at' => 'nullable|date',
        ]);

        $data = [
            'title' => $this->title,
            'slug' => $this->slug,
            'content' => $this->content,
            'excerpt' => $this->excerpt,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'featured_image' => $this->featured_image,
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'sort_order' => $this->sort_order,
            'published_at' => $this->published_at,
        ];

        $this->page = $this->pageService->update($this->page, $data);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function saveAsDraft()
    {
        $this->status = 'draft';
        $this->update();
    }

    public function saveAndPublish()
    {
        $this->status = 'published';
        if (empty($this->published_at)) {
            $this->published_at = now()->format('Y-m-d\TH:i');
        }
        $this->update();
    }

    public function archive()
    {
        if (!auth()->user()->can('pages.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->pageService->archive($this->page);
        $this->page->refresh();
        $this->status = $this->page->status;

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_archived_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function duplicate()
    {
        if (!auth()->user()->can('pages.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $newPage = $this->pageService->duplicate($this->page);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_duplicated_successfully'))->confirmButtonText(__('yes'))->show();

        return redirect()->route('pages.edit', $newPage->id);
    }

    public function generateSlug()
    {
        if (!empty($this->title)) {
            $this->slug = \Str::slug($this->title);
        }
    }

    public function switchLanguage($languageCode)
    {
        $this->currentLanguage = $languageCode;
        // إعادة تحميل البيانات للغة المحددة
        $this->loadPageDataForLanguage($languageCode);

        // Emit event to update editor content with the new content
        $this->dispatch('languageSwitched', $this->content);
    }

    protected function loadPageDataForLanguage($languageCode)
    {
        // تحميل البيانات المترجمة للغة المحددة
        $this->title = $this->page->getTranslation('title', $languageCode) ?? '';
        $this->content = $this->page->getTranslation('content', $languageCode) ?? '';
        $this->excerpt = $this->page->getTranslation('excerpt', $languageCode) ?? '';
        $this->meta_title = $this->page->getTranslation('meta_title', $languageCode) ?? '';
        $this->meta_description = $this->page->getTranslation('meta_description', $languageCode) ?? '';
        $this->meta_keywords = $this->page->getTranslation('meta_keywords', $languageCode) ?? [];
    }

    public function addKeyword()
    {
        $this->meta_keywords[] = '';
    }

    public function removeKeyword($index)
    {
        unset($this->meta_keywords[$index]);
        $this->meta_keywords = array_values($this->meta_keywords);
    }

    public function preview()
    {
        // يمكن إضافة منطق المعاينة هنا
        (new LivewireAlert($this))->info()->title(__('info'))->text(__('preview_feature_coming_soon'))->confirmButtonText(__('yes'))->show();
    }

    public function cancel()
    {
        return redirect()->route('pages.index');
    }

    public function delete()
    {
        if (!auth()->user()->can('pages.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->pageService->delete($this->page);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_deleted_successfully'))->confirmButtonText(__('yes'))->show();

        return redirect()->route('pages.index');
    }
}
