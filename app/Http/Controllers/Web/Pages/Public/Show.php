<?php

namespace App\Http\Controllers\Web\Pages\Public;

use Livewire\Component;
use App\Models\Page;

class Show extends Component
{
    public Page $page;
    public string $slug;

    public function mount(string $slug)
    {
        $this->slug = $slug;
        $this->page = Page::where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();
    }

    public function render()
    {
        return view('pages.pages.public.show')
            ->layout('layouts.public', [
                'title' => $this->page->meta_title ?: $this->page->title,
                'description' => $this->page->meta_description ?: $this->page->excerpt,
            ]);
    }
}
