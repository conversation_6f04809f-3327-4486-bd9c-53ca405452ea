<?php

namespace App\Http\Controllers\Web\Orders;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\Station;
use App\Models\Type;
use App\Models\User;
use App\Services\OrderService;
use Jantinnerezo\LivewireAlert\Concerns\SweetAlert2;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    use SweetAlert2;
    protected $paginationTheme = 'bootstrap';
    protected array $paginationOptions = [
        'bootstrap' => true,
        'align' => 'center',
        'size' => 'default',
    ];
    protected OrderService $orderService;

    // Filters
    public $status = '';
    public $fromStationId = '';
    public $toStationId = '';
    public $typeId = '';
    public $userId = '';
    public $search = '';
    public $dateFrom = '';
    public $dateTo = '';
    public $perPage = 15;
    public $activeTab = 'all'; // all, stuck

    // Data
    public $stations = [];
    public $types = [];
    public $users = [];
    public $statusOptions = [];
    public $statistics = [];

    protected $queryString = [
        'status' => ['except' => ''],
        'search' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    public function boot(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function mount()
    {
        $this->loadFilterData();
        $this->loadStatistics();
        $this->createTestOrdersIfNeeded();
    }

    public function render()
    {
        $orders = $this->getOrders();

        return view('pages.orders.index', [
            'orders' => $orders,
        ]);
    }

    public function switchTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatus()
    {
        $this->resetPage();
    }

    public function updatedFromStationId()
    {
        $this->resetPage();
    }

    public function updatedToStationId()
    {
        $this->resetPage();
    }

    public function updatedTypeId()
    {
        $this->resetPage();
    }

    public function updatedUserId()
    {
        $this->resetPage();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->reset([
            'status',
            'fromStationId',
            'toStationId',
            'typeId',
            'userId',
            'search',
            'dateFrom',
            'dateTo',
        ]);
        $this->resetPage();
    }

    public function refreshStatistics()
    {
        $this->loadStatistics();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('statistics_updated'))->confirmButtonText(__('yes'))->show();
    }

    private function getOrders()
    {
        // If stuck tab is active, return stuck orders
        if ($this->activeTab === 'stuck') {
            return $this->orderService->getStuckOrders($this->perPage);
        }

        $filters = array_filter([
            'status' => $this->status,
            'from_station_id' => $this->fromStationId,
            'to_station_id' => $this->toStationId,
            'type_id' => $this->typeId,
            'user_id' => $this->userId,
            'barcode' => $this->search,
            'receiver_name' => $this->search,
            'receiver_phone' => $this->search,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
        ]);

        if (!empty($this->search)) {
            return $this->orderService->searchOrders($this->search, $this->perPage);
        }

        return $this->orderService->getAll($this->perPage, false, $filters);
    }

    private function loadFilterData()
    {
        $this->stations = Station::select('id', 'name')->orderBy('name')->get();
        $this->types = Type::select('id', 'name')->orderBy('name')->get();
        $this->users = User::select('id', 'name', 'phone')->orderBy('name')->limit(100)->get();
        $this->statusOptions = OrderStatus::getSelectOptions();
    }

    private function loadStatistics()
    {
        $this->statistics = $this->orderService->getOrderStatistics();
        $this->statistics['stuck'] = $this->orderService->getStuckOrdersCount();
    }

    public function viewOrder($orderId)
    {
        return redirect()->route('orders.show', $orderId);
    }

    public function editOrder($orderId)
    {
        return redirect()->route('orders.edit', $orderId);
    }

    public function deleteOrder($orderId)
    {
        if (!auth()->user()->can('orders.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        try {
            $order = Order::findOrFail($orderId);

            (new LivewireAlert($this))
                ->question()
                ->title(__('confirm_delete'))
                ->text(__('are_you_sure_delete') . ' ' . __('order') . ' ' . $order->barcode . '?')
                ->showConfirmButton()
                ->showCancelButton()
                ->confirmButtonText(__('yes_delete'))
                ->cancelButtonText(__('cancel'))
                ->confirmButtonColor('#d33')
                ->cancelButtonColor('#3085d6')
                ->onConfirmed('confirmDeleteOrder', ['orderId' => $orderId])
                ->show();
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('order_not_found'))->confirmButtonText(__('yes'))->show();
        }
    }

    public function confirmDeleteOrder($data)
    {
        try {
            $order = Order::findOrFail($data['orderId']);
            $this->orderService->delete($order);
            (new LivewireAlert($this))->success()->title(__('success'))->text(__('order_deleted_successfully'))->confirmButtonText(__('yes'))->show();
            $this->loadStatistics();
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('something_went_wrong'))->confirmButtonText(__('yes'))->show();
        }
    }

    public function exportOrders()
    {
        // TODO: Implement export functionality
        (new LivewireAlert($this))->info()->title(__('info'))->text(__('export_feature_coming_soon'))->confirmButtonText(__('yes'))->show();
    }

    public function bulkAction($action, $orderIds)
    {
        try {
            if (!auth()->user()->can('orders.edit')) {
                (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();

                return;
            }

            $orders = Order::whereIn('id', $orderIds)->get();

            foreach ($orders as $order) {
                switch ($action) {
                    case 'delete':
                        $this->orderService->delete($order);
                        break;
                    case 'confirm':
                        if ($order->status === 'pending') {
                            $this->orderService->updateStatus($order, 'confirmed', 'Bulk confirmed by admin');
                        }
                        break;
                    case 'cancel':
                        if (in_array($order->status, ['pending', 'confirmed', 'picked_up'])) {
                            $status = match ($order->status) {
                                'pending' => 'canceled_before_timed_out_by_admin',
                                'confirmed' => 'canceled_after_confirmed_by_admin',
                                'picked_up' => 'canceled_after_picked_up_by_admin',
                            };
                            $this->orderService->updateStatus($order, $status, 'Bulk canceled by admin');
                        }
                        break;
                }
            }

            (new LivewireAlert($this))->success()->title(__('success'))->text(__('bulk_action_completed'))->confirmButtonText(__('yes'))->show();
            $this->loadStatistics();
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('something_went_wrong'))->confirmButtonText(__('yes'))->show();
        }
    }

    private function createTestOrdersIfNeeded()
    {
        // Create test orders if they don't exist
        $orderCount = Order::count();
        if ($orderCount < 5) {
            $user = User::first();
            $stations = Station::take(2)->get();
            $type = Type::first();

            if ($user && $stations->count() >= 2 && $type) {
                for ($i = 1; $i <= 5; ++$i) {
                    Order::create([
                        'barcode' => 'ORD'.str_pad($i, 6, '0', STR_PAD_LEFT),
                        'user_id' => $user->id,
                        'from_station_id' => $stations[0]->id,
                        'to_station_id' => $stations[1]->id,
                        'type_id' => $type->id,
                        'receiver_name' => 'Receiver '.$i,
                        'receiver_phone' => '05'.rand(10000000, 99999999),
                        'price' => rand(50, 200),
                        'status' => ['pending', 'confirmed', 'picked_up', 'delivered'][rand(0, 3)],
                        'note' => 'Test order '.$i,
                        'created_by' => $user->id,
                        'updated_by' => $user->id,
                    ]);
                }
            }
        }
    }
}
