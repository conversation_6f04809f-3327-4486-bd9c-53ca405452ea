<?php

namespace App\Http\Controllers\Web\Orders;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Services\OrderService;
use Livewire\Component;
use Jan<PERSON>nerezo\LivewireAlert\Concerns\SweetAlert2;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;

class Show extends Component
{
    use SweetAlert2;

    protected OrderService $orderService;

    public Order $order;
    public $newStatus = '';
    public $statusNote = '';
    public $trackings = [];
    public $allowedStatuses = [];
    public $showStatusModal = false;

    public function boot(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function mount(Order $order)
    {
        $this->order = $order->load([
            'fromStation',
            'toStation',
            'type',
            'user',
            'trackings.user',
            'orderDriver.driver'
        ]);

        $this->loadTrackings();
        $this->loadAllowedStatuses();
    }

    public function render()
    {
        return view('pages.orders.show');
    }

    public function openStatusModal()
    {
        if (!auth()->user()->can('orders.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->showStatusModal = true;
        $this->newStatus = '';
        $this->statusNote = '';
    }

    public function closeStatusModal()
    {
        $this->showStatusModal = false;
        $this->newStatus = '';
        $this->statusNote = '';
    }

    public function updateStatus()
    {
        $this->validate([
            'newStatus' => 'required|in:' . implode(',', array_keys($this->allowedStatuses)),
            'statusNote' => 'nullable|string|max:500',
        ]);

        if (!auth()->user()->can('orders.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        if ($this->newStatus === $this->order->status) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('status_must_be_different_from_current'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $statusEnum = \App\Enums\OrderStatus::from($this->newStatus);

        (new LivewireAlert($this))
            ->question()
            ->title(__('confirm_status_change'))
            ->text(__('are_you_sure_change_status_to') . ' ' . $statusEnum->label() . '?')
            ->showConfirmButton()
            ->showCancelButton()
            ->confirmButtonText(__('yes_change'))
            ->cancelButtonText(__('cancel'))
            ->confirmButtonColor('#3085d6')
            ->cancelButtonColor('#d33')
            ->onConfirmed('confirmUpdateStatus')
            ->show();
    }

    public function confirmUpdateStatus()
    {
        try {
            $this->orderService->updateStatus(
                $this->order,
                $this->newStatus,
                $this->statusNote ?: 'Status updated by admin'
            );

            $this->order->refresh();
            $this->loadTrackings();
            $this->loadAllowedStatuses();
            $this->closeStatusModal();

            (new LivewireAlert($this))->success()->title(__('success'))->text(__('order_status_updated_successfully'))->confirmButtonText(__('yes'))->show();
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('something_went_wrong') . ': ' . $e->getMessage())->confirmButtonText(__('yes'))->show();
        }
    }

    public function refreshOrder()
    {
        $this->order->refresh();
        $this->loadTrackings();
        $this->loadAllowedStatuses();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('order_refreshed'))->confirmButtonText(__('yes'))->show();
    }

    public function deleteOrder()
    {
        if (!auth()->user()->can('orders.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        (new LivewireAlert($this))
            ->question()
            ->title(__('confirm_delete'))
            ->text(__('are_you_sure_delete') . ' ' . __('order') . ' ' . $this->order->barcode . '?')
            ->showConfirmButton()
            ->showCancelButton()
            ->confirmButtonText(__('yes_delete'))
            ->cancelButtonText(__('cancel'))
            ->confirmButtonColor('#d33')
            ->cancelButtonColor('#3085d6')
            ->onConfirmed('confirmDeleteOrder')
            ->show();
    }

    public function confirmDeleteOrder()
    {
        try {
            $this->orderService->delete($this->order);
            (new LivewireAlert($this))->success()->title(__('success'))->text(__('order_deleted_successfully'))->confirmButtonText(__('yes'))->show();

            return redirect()->route('orders.index');
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('something_went_wrong'))->confirmButtonText(__('yes'))->show();
        }
    }

    public function printOrder()
    {
        // TODO: Implement print functionality
        (new LivewireAlert($this))->info()->title(__('info'))->text(__('print_feature_coming_soon'))->confirmButtonText(__('yes'))->show();
    }

    public function exportOrderDetails()
    {
        // TODO: Implement export functionality
        (new LivewireAlert($this))->info()->title(__('info'))->text(__('export_feature_coming_soon'))->confirmButtonText(__('yes'))->show();
    }

    private function loadTrackings()
    {
        $this->trackings = $this->order->trackings()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    private function loadAllowedStatuses()
    {
        $currentStatus = OrderStatus::from($this->order->status);
        $allowedNext = $currentStatus->getAllowedNextStatuses();

        $this->allowedStatuses = [];
        foreach ($allowedNext as $status) {
            $this->allowedStatuses[$status->value] = $status->label();
        }
    }

    public function getStatusBadgeProperty()
    {
        $status = OrderStatus::from($this->order->status);
        return [
            'color' => $status->color(),
            'icon' => $status->icon(),
            'label' => $status->label(),
        ];
    }

    public function getOrderProgressProperty()
    {
        $status = OrderStatus::from($this->order->status);

        $progress = match($status) {
            OrderStatus::PENDING => 25,
            OrderStatus::CONFIRMED => 50,
            OrderStatus::PICKED_UP => 75,
            OrderStatus::DELIVERED => 100,
            default => 0, // Canceled statuses
        };

        return [
            'percentage' => $progress,
            'color' => $status->isCanceled() ? 'danger' : 'primary',
        ];
    }

    public function getTimelineStepsProperty()
    {
        $currentStatus = OrderStatus::from($this->order->status);

        $steps = [
            [
                'status' => OrderStatus::PENDING,
                'title' => __('pending'),
                'description' => __('order_created'),
                'completed' => true,
                'active' => $currentStatus === OrderStatus::PENDING,
            ],
            [
                'status' => OrderStatus::CONFIRMED,
                'title' => __('confirmed'),
                'description' => __('order_confirmed'),
                'completed' => in_array($currentStatus, [OrderStatus::CONFIRMED, OrderStatus::PICKED_UP, OrderStatus::DELIVERED]),
                'active' => $currentStatus === OrderStatus::CONFIRMED,
            ],
            [
                'status' => OrderStatus::PICKED_UP,
                'title' => __('picked_up'),
                'description' => __('order_picked_up'),
                'completed' => in_array($currentStatus, [OrderStatus::PICKED_UP, OrderStatus::DELIVERED]),
                'active' => $currentStatus === OrderStatus::PICKED_UP,
            ],
            [
                'status' => OrderStatus::DELIVERED,
                'title' => __('delivered'),
                'description' => __('order_delivered'),
                'completed' => $currentStatus === OrderStatus::DELIVERED,
                'active' => $currentStatus === OrderStatus::DELIVERED,
            ],
        ];

        // Mark as canceled if current status is canceled
        if ($currentStatus->isCanceled()) {
            foreach ($steps as &$step) {
                if (!$step['completed']) {
                    $step['canceled'] = true;
                }
            }
        }

        return $steps;
    }

    public function quickStatusUpdate($status)
    {
        if (!auth()->user()->can('orders.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        if (!in_array($status, array_keys($this->allowedStatuses))) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('invalid_status_transition'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $statusEnum = \App\Enums\OrderStatus::from($status);

        (new LivewireAlert($this))
            ->question()
            ->title(__('confirm_status_change'))
            ->text(__('are_you_sure_change_status_to') . ' ' . $statusEnum->label() . '?')
            ->showConfirmButton()
            ->showCancelButton()
            ->confirmButtonText(__('yes_change'))
            ->cancelButtonText(__('cancel'))
            ->confirmButtonColor('#3085d6')
            ->cancelButtonColor('#d33')
            ->onConfirmed('confirmQuickStatusUpdate', ['status' => $status])
            ->show();
    }

    public function confirmQuickStatusUpdate($data)
    {
        $status = $data['status'];

        try {
            $this->orderService->updateStatus(
                $this->order,
                $status,
                'Quick status update by admin'
            );

            $this->order->refresh();
            $this->loadTrackings();
            $this->loadAllowedStatuses();

            (new LivewireAlert($this))->success()->title(__('success'))->text(__('order_status_updated_successfully'))->confirmButtonText(__('yes'))->show();
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('something_went_wrong'))->confirmButtonText(__('yes'))->show();
        }
    }

}
