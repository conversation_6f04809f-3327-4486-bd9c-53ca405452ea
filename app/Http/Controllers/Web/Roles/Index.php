<?php

namespace App\Http\Controllers\Web\Roles;

use App\Services\RoleService;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    protected array $paginationOptions = [
        'bootstrap' => true,
        'align' => 'center',
        'size' => 'default',
    ];

    public $showCreateModal = false;
    public $showEditModal = false;
    public $showViewModal = false;
    public $showDeleteModal = false;
    public $selectedRoleId;
    public $name;
    public $guard_name = 'web';
    public $permissions = [];

    protected $roleService;

    public function boot(RoleService $roleService)
    {
        $this->roleService = $roleService;
    }

    public function render()
    {
        $permissions = \Spatie\Permission\Models\Permission::all();
        $groupedPermissions = $this->groupPermissions($permissions);

        return view('pages.roles.index', [
            'roles' => $this->roleService->getAllRoles(10),
            'groupedPermissions' => $groupedPermissions,
        ]);
    }

    private function groupPermissions($permissions)
    {
        $grouped = [];
        foreach ($permissions as $permission) {
            $parts = explode('.', $permission->name);
            $module = $parts[0];
            $action = $parts[1] ?? 'view';

            if (!isset($grouped[$module])) {
                $grouped[$module] = [];
            }

            $grouped[$module][] = [
                'name' => $permission->name,
                'label' => __($module.'.'.$action),
            ];
        }

        return $grouped;
    }

    public function create()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        $this->validate([
            'name' => 'required|string|max:255|unique:roles',
            'guard_name' => 'required|string|max:255',
        ]);

        $role = $this->roleService->createRole([
            'name' => $this->name,
            'guard_name' => $this->guard_name,
        ]);

        if (!empty($this->permissions)) {
            $role->syncPermissions($this->permissions);
        }

        $this->resetForm();
        $this->showCreateModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('role_created_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        $role = $this->roleService->getRoleById($id);
        $this->selectedRoleId = $id;
        $this->name = $role->name;
        $this->guard_name = $role->guard_name;
        $this->permissions = $role->permissions->pluck('name')->toArray();
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->validate([
            'name' => 'required|string|max:255|unique:roles,name,'.$this->selectedRoleId,
            'guard_name' => 'required|string|max:255',
        ]);

        $role = $this->roleService->updateRole($this->selectedRoleId, [
            'name' => $this->name,
            'guard_name' => $this->guard_name,
        ]);

        $role->syncPermissions($this->permissions);

        $this->resetForm();
        $this->showEditModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('role_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function show($id)
    {
        $this->selectedRoleId = $id;
        $this->showViewModal = true;
    }

    public function confirmDelete($id)
    {
        $this->selectedRoleId = $id;
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        $this->roleService->deleteRole($this->selectedRoleId);
        $this->showDeleteModal = false;
        $this->selectedRoleId = null;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('role_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function resetForm()
    {
        $this->name = '';
        $this->guard_name = 'web';
        $this->permissions = [];
        $this->selectedRoleId = null;
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showViewModal = false;
        $this->showDeleteModal = false;
        $this->resetForm();
    }
}
