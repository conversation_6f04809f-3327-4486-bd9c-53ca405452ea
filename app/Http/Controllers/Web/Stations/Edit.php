<?php

namespace App\Http\Controllers\Web\Stations;

use App\Services\StationService;
use <PERSON><PERSON><PERSON>ez<PERSON>\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Edit extends Component
{
    protected StationService $stationService;
    
    public $station, $name, $latitude, $longitude;

    public function boot(StationService $stationService)
    {
        $this->stationService = $stationService;
    }

    public function mount($id)
    {
        $this->station = $this->stationService->findById($id);
        $this->name = $this->station->name;
        $this->latitude = $this->station->latitude;
        $this->longitude = $this->station->longitude;
    }

    public function update()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $this->stationService->update($this->station, [
            'name' => $this->name,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ]);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_updated_successfully'))->confirmButtonText(__('yes'))->show();
        return redirect()->route('stations.index');
    }

    public function render()
    {
        return view('pages.stations.update')->layout('layouts.app', [
            'availableLanguages' => \App\Models\Language::all()
        ]);
    }
}