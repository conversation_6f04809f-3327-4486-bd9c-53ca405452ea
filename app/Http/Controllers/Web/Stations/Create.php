<?php

namespace App\Http\Controllers\Web\Stations;

use App\Services\StationService;
use <PERSON><PERSON><PERSON>ez<PERSON>\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Create extends Component
{
    protected StationService $stationService;
    
    public $name, $latitude, $longitude;

    public function boot(StationService $stationService)
    {
        $this->stationService = $stationService;
    }

    public function store()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $this->stationService->create([
            'name' => $this->name,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ]);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_created_successfully'))->confirmButtonText(__('yes'))->show();
        return redirect()->route('stations.index');
    }

    public function render()
    {
        return view('pages.stations.create')->layout('layouts.app', [
            'availableLanguages' => \App\Models\Language::all()
        ]);
    }
}