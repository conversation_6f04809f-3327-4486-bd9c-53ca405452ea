<?php

namespace App\Http\Controllers\Web\Dashboard;

use Livewire\Component;
use App\Services\OrderService;
use App\Models\User;
use App\Models\Station;
use App\Models\Type;
use App\Models\Order;
use App\Enums\OrderStatus;

class Index extends Component
{
    protected OrderService $orderService;

    public $dashboardStats = [];
    public $recentOrders = [];
    public $stuckOrders = [];

    public function boot(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function render()
    {
        return view('pages.dashboard.index', [
            'dashboardStats' => $this->dashboardStats,
            'recentOrders' => $this->recentOrders,
            'stuckOrders' => $this->stuckOrders,
        ]);
    }

    public function refreshStats()
    {
        $this->loadDashboardData();
        session()->flash('message', __('statistics_updated'));
    }

    private function loadDashboardData()
    {
        // Get dashboard statistics
        $this->dashboardStats = $this->orderService->getDashboardStatistics();

        // Get recent orders (last 10)
        $this->recentOrders = Order::with(['user', 'fromStation', 'toStation', 'type'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Get stuck orders (first 5)
        $this->stuckOrders = $this->orderService->getStuckOrders(5)->items();

        // Add additional stats
        $this->dashboardStats['total_users'] = User::count();
        $this->dashboardStats['total_stations'] = Station::count();
        $this->dashboardStats['total_types'] = Type::count();
        $this->dashboardStats['active_users'] = User::whereHas('orders', function($q) {
            $q->whereDate('created_at', '>=', now()->subDays(30));
        })->count();
    }
}
