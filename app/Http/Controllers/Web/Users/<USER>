<?php

namespace App\Http\Controllers\Web\Users;

use App\Services\UserService;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    protected array $paginationOptions = [
        'bootstrap' => true,
        'align' => 'center',
        'size' => 'default',
    ];

    public $showCreateModal = false;
    public $showEditModal = false;
    public $showViewModal = false;
    public $showDeleteModal = false;
    public $selectedUserId;
    public $user; // المستخدم المحدد للتعديل
    public $name;
    public $email;
    public $password;
    public $role;

    // Filter properties
    public $search = '';
    public $selectedRole = '';
    public $dateFrom = '';
    public $dateTo = '';
    public $perPage = 10;

    protected $userService;

    public function boot(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function render()
    {
        return view('pages.users.index', [
            'users' => $this->userService->getAllUsers(
                $this->perPage,
                $this->search,
                $this->selectedRole,
                $this->dateFrom,
                $this->dateTo
            ),
            'roles' => $this->getRoles(),
        ]);
    }

    public function create()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'role' => 'required|exists:roles,name',
        ]);

        $this->userService->createUser([
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->password,
            'role' => $this->role,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('user_created_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        $this->user = $this->userService->getUserById($id);
        $this->selectedUserId = $id;
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        $this->role = $this->user->roles->first()?->name;
        $this->showEditModal = true;
    }

    public function update()
    {
        $validationRules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,'.$this->selectedUserId,
        ];

        // إضافة قاعدة التحقق من الدور فقط إذا لم يكن سوبر أدمن
        if (!$this->user || !$this->user->hasRole('super_admin')) {
            $validationRules['role'] = 'required|exists:roles,name';
        }

        $this->validate($validationRules);

        $data = [
            'name' => $this->name,
            'email' => $this->email,
        ];

        // إضافة الدور فقط إذا لم يكن سوبر أدمن
        if (!$this->user || !$this->user->hasRole('super_admin')) {
            $data['role'] = $this->role;
        }

        if ($this->password) {
            $data['password'] = $this->password;
        }

        $this->userService->updateUser($this->selectedUserId, $data);

        $this->resetForm();
        $this->showEditModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('user_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function show($id)
    {
        $this->selectedUserId = $id;
        $this->showViewModal = true;
    }

    public function confirmDelete($id)
    {
        $user = $this->userService->getUserById($id);

        // التحقق من أن المستخدم ليس سوبر أدمن أو أدمن
        if ($user->hasRole(['super_admin', 'admin'])) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('Cannot delete admin or super admin user'))->confirmButtonText(__('ok'))->show();
            return;
        }

        $this->selectedUserId = $id;
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        $user = $this->userService->getUserById($this->selectedUserId);

        // التحقق مرة أخرى قبل الحذف
        if ($user->hasRole(['super_admin', 'admin'])) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('Cannot delete admin or super admin user'))->confirmButtonText(__('ok'))->show();
            return;
        }

        $this->userService->deleteUser($this->selectedUserId);
        $this->showDeleteModal = false;
        $this->selectedUserId = null;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('user_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function resetForm()
    {
        $this->name = '';
        $this->email = '';
        $this->password = '';
        $this->role = '';
        $this->selectedUserId = null;
        $this->user = null;
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showViewModal = false;
        $this->showDeleteModal = false;
        $this->resetForm();
    }

    // Filter methods
    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedSelectedRole()
    {
        $this->resetPage();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->reset(['search', 'selectedRole', 'dateFrom', 'dateTo']);
        $this->resetPage();
    }

    public function getRoles()
    {
        return \Spatie\Permission\Models\Role::all();
    }
}
