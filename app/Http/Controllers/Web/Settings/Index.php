<?php

namespace App\Http\Controllers\Web\Settings;

use App\Models\Setting;
use Livewire\Component;
use Livewire\WithFileUploads;

class Index extends Component
{
    use WithFileUploads;

    public $settings = [];
    public $activeTab = 'general';

    public function mount()
    {
        $this->loadSettings();
    }

    public function loadSettings()
    {
        $allSettings = Setting::all();
        foreach ($allSettings as $setting) {
            $this->settings[$setting->key] = $setting->value;
        }
    }

    public function save()
    {
        foreach ($this->settings as $key => $value) {
            if (is_object($value) && method_exists($value, 'store')) {
                // Handle file uploads
                $path = $value->store('settings', 'public');
                $value = $path;
            }
            
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        session()->flash('success', __('settings_updated_successfully'));
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function render()
    {
        $settingGroups = Setting::select('group')->distinct()->pluck('group');
        $groupedSettings = Setting::all()->groupBy('group');
        
        return view('pages.settings.index', [
            'settingGroups' => $settingGroups,
            'groupedSettings' => $groupedSettings
        ])->layout('layouts.app');
    }
}