<?php

namespace App\Http\Controllers\Web\Languages;

use App\Services\LanguageService;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    protected array $paginationOptions = [
        'bootstrap' => true,
        'align' => 'center',
        'size' => 'default',
    ];
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showViewModal = false;
    public $showDeleteModal = false;
    public $selectedLanguageId;
    public $name;
    public $code;
    public $flag;
    public $direction;
    public $is_active = true;

    protected $languageService;

    public function boot(LanguageService $languageService)
    {
        $this->languageService = $languageService;
    }

    public function render()
    {
        return view('pages.languages.index', [
            'languages' => $this->languageService->getAllLanguages(10),
        ]);
    }

    public function create()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:5|unique:languages',
            'flag' => 'required|string|max:10',
            'direction' => 'required|in:ltr,rtl',
        ]);

        $this->languageService->createLanguage([
            'name' => $this->name,
            'code' => $this->code,
            'flag' => $this->flag,
            'direction' => $this->direction,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('language_created_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        $language = $this->languageService->getLanguageById($id);
        $this->selectedLanguageId = $id;
        $this->name = $language->name;
        $this->code = $language->code;
        $this->flag = $language->flag;
        $this->direction = $language->direction;
        $this->is_active = $language->is_active;
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:5|unique:languages,code,'.$this->selectedLanguageId,
            'flag' => 'required|string|max:10',
            'direction' => 'required|in:ltr,rtl',
        ]);

        $this->languageService->updateLanguage($this->selectedLanguageId, [
            'name' => $this->name,
            'code' => $this->code,
            'flag' => $this->flag,
            'direction' => $this->direction,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('language_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function show($id)
    {
        $this->selectedLanguageId = $id;
        $this->showViewModal = true;
    }

    public function confirmDelete($id)
    {
        $this->selectedLanguageId = $id;
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        $this->languageService->deleteLanguage($this->selectedLanguageId);
        $this->showDeleteModal = false;
        $this->selectedLanguageId = null;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('language_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function resetForm()
    {
        $this->name = '';
        $this->code = '';
        $this->flag = '';
        $this->direction = 'ltr';
        $this->is_active = true;
        $this->selectedLanguageId = null;
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showViewModal = false;
        $this->showDeleteModal = false;
        $this->resetForm();
    }
}
