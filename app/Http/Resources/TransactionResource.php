<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'type_label' => $this->type === 'earned' ? __('earned') : __('spent'),
            'amount' => $this->amount,
            'formatted_amount' => number_format($this->amount),
            'amount_with_sign' => ($this->type === 'earned' ? '+' : '-') . number_format($this->amount),
            'description' => $this->description,
            'balance_after' => $this->balance_after,
            'formatted_balance_after' => number_format($this->balance_after ?? 0),
            'order_id' => $this->order_id,
            'order' => $this->whenLoaded('order', function () {
                return new OrderResource($this->order);
            }),
            'created_at' => $this->created_at,
            'created_at_formatted' => $this->created_at->format('Y-m-d H:i'),
            'created_at_human' => $this->created_at->diffForHumans(),
            'created_at_date' => $this->created_at->format('Y-m-d'),
            'created_at_time' => $this->created_at->format('H:i'),
        ];
    }
}
