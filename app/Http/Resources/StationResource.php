<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class StationResource extends BaseResource
{
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            // 'created_at' => $this->created_at,
            // 'updated_at' => $this->updated_at,
        ];

        // إضافة المسافة إذا كانت متوفرة (عندما يتم الترتيب حسب المسافة)
        if (isset($this->distance)) {
            $data['distance'] = round($this->distance, 2); // المسافة بالكيلومتر مع تقريب لرقمين عشريين
        }

        return $data;
    }
}
