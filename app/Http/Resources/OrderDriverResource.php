<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDriverResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $settings = \App\Models\Setting::whereIn('key', ['cash_to_points_rate', 'delivery_deadline_minutes'])
            ->pluck('value', 'key');

        // Get and calculate delivery deadline
        $deliveryDeadlineMinutes = (int) ($settings['delivery_deadline_minutes'] ?? 30);

        return [
            'id' => $this->id,
            'from_station' => new StationResource($this->fromStation),
            'to_station' => new StationResource($this->toStation),
            'type' => $this->type?->name,
            'status' => 'waiting_for_order',
            'status_text' => __('waiting_for_order'),
            'delivery_deadline' => [
                'minutes' => $deliveryDeadlineMinutes,
                // 'datetime' => $deliveryDeadline,
                // 'remaining_time' => $formattedTime,
            ],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_interest' => true,
        ];
    }
}
