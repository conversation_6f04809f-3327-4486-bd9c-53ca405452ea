<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuthResource extends JsonResource
{
    protected $token;
    protected $message;

    public function __construct($resource, $token = null, $message = null)
    {
        parent::__construct($resource);
        $this->token = $token;
        $this->message = $message;
    }

    public function toArray(Request $request): array
    {
        $data = [
            'user' => new UserResource($this->resource),
        ];

        if ($this->token) {
            $data['token'] = $this->token;
        }

        if ($this->message) {
            $data['message'] = $this->message;
        }

        return $data;
    }
}