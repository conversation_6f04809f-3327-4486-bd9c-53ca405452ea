<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

class BaseResource extends JsonResource
{
    public function with(Request $request): array
    {
        return [
            'success' => true,
            'message' => $this->message ?? null,
        ];
    }

    public static function collection($resource)
    {
        return new class($resource, static::class) extends ResourceCollection {
            public function toArray($request)
            {
                return [
                    'success' => true,
                    'data' => $this->collection,
                    'pagination' => [
                        'current_page' => $this->currentPage(),
                        'last_page' => $this->lastPage(),
                        'per_page' => $this->perPage(),
                        'total' => $this->total(),
                    ]
                ];
            }
        };
    }
}