<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliverySearchResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'type_name' => $this['type_name'],
            'shipments_count' => $this['shipments_count'],
            'interested_people' => $this['interested_people'],
            'delivery_chance' => $this['delivery_chance'],
        ];
    }
}