<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WalletResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'balance' => (int) $this['balance'],
            'total_earned' => (int) $this['total_earned'],
            'total_spent' => (int) $this['total_spent'],
            'transactions_count' => (int) $this['transactions_count'],
        ];
    }
}
