<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'content' => $this->content,
            'excerpt' => $this->excerpt,
            'featured_image' => $this->featured_image,
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'sort_order' => $this->sort_order,
            'published_at' => $this->published_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // SEO Meta Data
            'meta' => [
                'title' => $this->meta_title,
                'description' => $this->meta_description,
                'keywords' => $this->meta_keywords,
            ],

            // Author Information
            'author' => [
                'id' => $this->creator?->id,
                'name' => $this->creator?->name,
                'email' => $this->creator?->email,
            ],

            // Translations (if available)
            'translations' => $this->when($this->relationLoaded('translations'), function () {
                return $this->translations->mapWithKeys(function ($translation) {
                    return [$translation->locale => [
                        'title' => $translation->title,
                        'content' => $translation->content,
                        'excerpt' => $translation->excerpt,
                        'meta_title' => $translation->meta_title,
                        'meta_description' => $translation->meta_description,
                        'meta_keywords' => $translation->meta_keywords,
                    ]];
                });
            }),

            // URLs
            // 'urls' => [
            //     'public' => route('page.show', $this->slug),
            //     'edit' => route('pages.edit', $this->id),
            //     'api' => route('api.pages.show', $this->id),
            // ],
        ];
    }
}
