<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type->name,
            'from_station' => $this->fromStation->name,
            'to_station' => $this->toStation->name,
            'created_at' => $this->created_at,
        ];
    }
}