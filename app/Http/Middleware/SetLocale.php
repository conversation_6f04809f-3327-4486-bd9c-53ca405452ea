<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = session('locale', config('app.locale'));
        
        $availableLocales = \App\Models\Language::where('is_active', true)->pluck('code')->toArray();
        
        if (in_array($locale, $availableLocales)) {
            app()->setLocale($locale);
        }
        
        return $next($request);
    }
}
