<?php

namespace App\Http\Requests\Api;

use App\Models\Order;
use App\Models\Setting;
use App\Enums\OrderStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class CancelOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'order_id' => 'required|integer|exists:orders,id',
            'cancellation_note' => 'nullable|string|max:500',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function ($validator) {
            if ($this->order_id) {
                $userId = auth()->id();

                // Find the order
                $order = Order::find($this->order_id);

                if (!$order) {
                    $validator->errors()->add('order_id', 'الطلب غير موجود');
                    return;
                }

                // Check if user owns the order
                if ($order->user_id !== $userId) {
                    $validator->errors()->add('order_id', 'لا تملك صلاحية لإلغاء هذا الطلب');
                    return;
                }

                // Check if order status is pending
                if ($order->status !== OrderStatus::PENDING->value) {
                    $validator->errors()->add('order_id', 'لا يمكن إلغاء الطلب إلا إذا كان في حالة انتظار');
                    return;
                }

                // Check if still within allowed time
                $deliveryDeadlineMinutes = (int) Setting::get('delivery_deadline_minutes', 30);
                $cutoffTime = now()->subMinutes($deliveryDeadlineMinutes);

                if ($order->created_at <= $cutoffTime) {
                    $validator->errors()->add('order_id', 'لا يمكن إلغاء الطلب بعد انتهاء الوقت المسموح');
                    return;
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'order_id.required' => 'معرف الطلب مطلوب',
            'order_id.integer' => 'معرف الطلب يجب أن يكون رقم صحيح',
            'order_id.exists' => 'الطلب غير موجود',
            'cancellation_note.string' => 'ملاحظة الإلغاء يجب أن تكون نص',
            'cancellation_note.max' => 'ملاحظة الإلغاء يجب ألا تتجاوز 500 حرف',
        ];
    }
}
