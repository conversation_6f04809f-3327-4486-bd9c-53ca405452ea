<?php

namespace App\Http\Requests\Api;

use App\Models\Order;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class UpdateOrderStatusRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => 'required|exists:orders,id',
            'status' => [
                'required',
                Rule::in([
                    'pending',
                    'canceled_before_timed_out_by_user',
                    'canceled_before_timed_out_by_admin',
                    'canceled_timed_out_by_system',
                    'confirmed',
                    'canceled_after_confirmed_by_admin',
                    'picked_up',
                    'canceled_after_picked_up_by_admin',
                    'delivered',
                    'canceled_after_delivered_by_admin'
                ])
            ],
            'note' => 'nullable|string',
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            if ($this->order_id && $this->status) {
                $order = Order::find($this->order_id);
                
                if ($order && $order->status === $this->status) {
                    $validator->errors()->add('status', __('status_must_be_different_from_current'));
                }
            }
        });
    }
}