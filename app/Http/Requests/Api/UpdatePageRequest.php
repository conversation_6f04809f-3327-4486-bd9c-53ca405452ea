<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $pageId = $this->route('page');

        return [
            'title' => 'sometimes|required|array',
            'title.*' => 'sometimes|required|string|max:255',
            'slug' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('pages', 'slug')->ignore($pageId)
            ],
            'content' => 'nullable|array',
            'content.*' => 'nullable|string',
            'excerpt' => 'nullable|array',
            'excerpt.*' => 'nullable|string|max:500',
            'featured_image' => 'nullable|string|max:255',
            'status' => 'sometimes|required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'published_at' => 'nullable|date',

            // SEO Meta Data
            'meta_title' => 'nullable|array',
            'meta_title.*' => 'nullable|string|max:255',
            'meta_description' => 'nullable|array',
            'meta_description.*' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'meta_keywords.*' => 'nullable|array',
            'meta_keywords.*.*' => 'string|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required' => __('validation.required', ['attribute' => __('title')]),
            'title.*.required' => __('validation.required', ['attribute' => __('title')]),
            'slug.required' => __('validation.required', ['attribute' => __('slug')]),
            'slug.unique' => __('validation.unique', ['attribute' => __('slug')]),
            'status.required' => __('validation.required', ['attribute' => __('status')]),
            'status.in' => __('validation.in', ['attribute' => __('status')]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'title' => __('title'),
            'slug' => __('slug'),
            'content' => __('content'),
            'excerpt' => __('excerpt'),
            'status' => __('status'),
            'is_featured' => __('featured'),
            'sort_order' => __('sort_order'),
            'published_at' => __('publish_date'),
            'meta_title' => __('meta_title'),
            'meta_description' => __('meta_description'),
            'meta_keywords' => __('meta_keywords'),
        ];
    }
}
