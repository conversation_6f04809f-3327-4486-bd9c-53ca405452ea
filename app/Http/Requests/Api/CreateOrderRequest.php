<?php

namespace App\Http\Requests\Api;

use App\Models\Pricing;
use App\Models\Setting;
use App\Services\UserStatsService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class CreateOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'from_station_id' => 'required|exists:stations,id',
            'to_station_id' => 'required|exists:stations,id|different:from_station_id',
            'type_id' => 'required|exists:types,id',
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'required|string|max:20',
            'note' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,points,visa', // إضافة خيار الدفع
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $pricing = Pricing::where([
                'from_station_id' => $this->from_station_id,
                'to_station_id' => $this->to_station_id,
                'type_id' => $this->type_id,
            ])->first();

            if (!$pricing) {
                $validator->errors()->add('pricing', __('no_pricing_available_for_selected_route'));
                return;
            }

            if ($this->price != $pricing->price) {
                $validator->errors()->add('price', __('price_does_not_match_pricing_table'));
            }

            // التحقق من النقاط في حالة الدفع بالنقاط
            if ($this->payment_method === 'points') {
                $userStatsService = app(UserStatsService::class);
                $userStats = $userStatsService->getUserStats(auth()->id());

                // تحويل السعر إلى نقاط
                $pointsToCashRate = (float) Setting::get('points_to_cash_rate', 5);
                $requiredPoints = (int) ($this->price * $pointsToCashRate);

                // التحقق من كفاية النقاط (الحالي + المعلق)
                if ($userStats['total_points'] < $requiredPoints) {
                    $validator->errors()->add('payment_method',
                        __('insufficient_points_for_order', [
                            'required' => $requiredPoints,
                            'available' => $userStats['total_points']
                        ])
                    );
                }
            }
        });
    }
}
