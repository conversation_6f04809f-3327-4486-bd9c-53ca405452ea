<?php

namespace App\Http\Requests\Api;

use App\Models\Order;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class AssignDriverRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type_id' => 'required|exists:types,id',
            'from_station_id' => 'required|exists:stations,id',
            'to_station_id' => 'required|exists:stations,id',
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            if ($this->type_id && $this->from_station_id && $this->to_station_id) {
                $driverId = auth()->id();

                // التحقق من عدم وجود اهتمام غير مكتمل
                $pendingInterest = \App\Models\OrderDriver::where('driver_id', $driverId)
                    ->where(function ($q) {
                        $q->whereNull('completed_at');
                    })
                    ->exists();

                if ($pendingInterest) {
                    $validator->errors()->add('driver_id', __('pending_interest_exists'));

                    return;
                }

                // التحقق من وجود طلبات متاحة للنوع والمحطات المحددة
                $availableOrders = Order::where('user_id', '!=', $driverId)
                    ->where('type_id', $this->type_id)
                    ->where('from_station_id', $this->from_station_id)
                    ->where('to_station_id', $this->to_station_id)
                    ->where('status', 'confirmed')
                    ->whereDoesntHave('orderDriver')
                    ->exists();

                if (!$availableOrders) {
                    $validator->errors()->add('type_id', __('no_available_orders_for_type'));
                }
            }
        });
    }
}
