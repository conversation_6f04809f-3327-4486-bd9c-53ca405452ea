<?php

namespace App\Http\Requests\Api;

use App\Models\OrderDriver;
use App\Models\Setting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class CancelDriverInterestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'order_driver_id' => 'required|integer|exists:order_drivers,id',
            'cancellation_note' => 'nullable|string|max:500',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function ($validator) {
            if ($this->order_driver_id) {
                $driverId = auth()->id();

                // Find the order driver record
                $orderDriver = OrderDriver::where('id', $this->order_driver_id)
                    ->where('driver_id', $driverId)
                    ->first();

                if (!$orderDriver) {
                    $validator->errors()->add('order_driver_id', 'اهتمام السائق غير موجود أو لا تملك صلاحية للوصول إليه');
                    return;
                }

                // Check if it's an interest (no order assigned)
                if ($orderDriver->order_id !== null) {
                    $validator->errors()->add('order_driver_id', 'لا يمكن إلغاء طلب تم تخصيصه بالفعل');
                    return;
                }

                // Check if still within allowed time
                $deliveryDeadlineMinutes = (int) Setting::get('delivery_deadline_minutes', 30);
                $cutoffTime = now()->subMinutes($deliveryDeadlineMinutes);

                if ($orderDriver->created_at <= $cutoffTime) {
                    $validator->errors()->add('order_driver_id', 'لا يمكن إلغاء الاهتمام بعد انتهاء الوقت المسموح');
                    return;
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'order_driver_id.required' => 'معرف اهتمام السائق مطلوب',
            'order_driver_id.integer' => 'معرف اهتمام السائق يجب أن يكون رقم صحيح',
            'order_driver_id.exists' => 'اهتمام السائق غير موجود',
            'cancellation_note.string' => 'ملاحظة الإلغاء يجب أن تكون نص',
            'cancellation_note.max' => 'ملاحظة الإلغاء يجب ألا تتجاوز 500 حرف',
        ];
    }
}
