<?php

namespace App\Services;

use Spatie\Permission\Models\Role;

class RoleService
{
    public function getAllRoles($perPage = 10)
    {
        return Role::paginate($perPage);
    }

    public function getRoleById($id)
    {
        return Role::findOrFail($id);
    }

    public function createRole(array $data)
    {
        return Role::create($data);
    }

    public function updateRole($id, array $data)
    {
        $role = Role::findOrFail($id);
        $role->update($data);
        return $role;
    }

    public function deleteRole($id)
    {
        $role = Role::findOrFail($id);
        return $role->delete();
    }
}