<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderDriver;
use App\Models\PointTransaction;
use App\Models\Setting;
use App\Models\User;

class UserStatsService
{
    /**
     * Get comprehensive user statistics for buy card modal.
     */
    public function getUserStats(int $userId): array
    {
        $user = User::with(['points', 'orders', 'driverOrders'])->findOrFail($userId);

        // Get user points balance
        $currentBalance = $user->points->current_balance ?? 0;

        // Get pending points from order_drivers without order_id
        $pendingPoints = $this->getPendingPoints($userId);

        // Calculate total points (current + pending)
        $totalPoints = $currentBalance + $pendingPoints;

        // Get delivery statistics
        $deliveryStats = $this->getDeliveryStats($userId);

        // Get order statistics
        $orderStats = $this->getOrderStats($userId);

        // Get conversion rates from settings
        $conversionRates = $this->getConversionRates();

        return [
            'user' => $user,
            'current_balance' => $currentBalance,
            'pending_points' => $pendingPoints,
            'total_points' => $totalPoints,
            'delivery_stats' => $deliveryStats,
            'order_stats' => $orderStats,
            'conversion_rates' => $conversionRates,
        ];
    }

    /**
     * Get delivery statistics for user (orders delivered by user as driver).
     */
    private function getDeliveryStats(int $userId): array
    {
        // Orders delivered by user as driver
        $deliveredOrders = Order::whereHas('orderDriver', function ($q) use ($userId) {
            $q->where('driver_id', $userId);
        })->where('status', 'delivered')->count();

        // Orders currently being delivered by user
        $currentDeliveries = Order::whereHas('orderDriver', function ($q) use ($userId) {
            $q->where('driver_id', $userId);
        })->where('status', 'picked_up')->count();

        // Total orders assigned to user (including completed and current)
        $totalAssigned = OrderDriver::where('driver_id', $userId)
            ->whereNotNull('order_id')
            ->count();

        return [
            'delivered_orders' => $deliveredOrders,
            'current_deliveries' => $currentDeliveries,
            'total_assigned' => $totalAssigned,
        ];
    }

    /**
     * Get order statistics for user (orders created by user).
     */
    private function getOrderStats(int $userId): array
    {
        // Orders created by user
        $createdOrders = Order::where('user_id', $userId)->count();

        // Orders by status
        $pendingOrders = Order::where('user_id', $userId)->where('status', 'pending')->count();
        $confirmedOrders = Order::where('user_id', $userId)->where('status', 'confirmed')->count();
        $deliveredOrders = Order::where('user_id', $userId)->where('status', 'delivered')->count();
        $cancelledOrders = Order::where('user_id', $userId)->where('status', 'cancelled')->count();

        return [
            'created_orders' => $createdOrders,
            'pending_orders' => $pendingOrders,
            'confirmed_orders' => $confirmedOrders,
            'delivered_orders' => $deliveredOrders,
            'cancelled_orders' => $cancelledOrders,
        ];
    }

    /**
     * Get pending points from order_drivers without order_id.
     */
    private function getPendingPoints(int $userId): int
    {
        // Get order_drivers without order_id (pending deliveries)
        $pendingDeliveries = OrderDriver::where('driver_id', $userId)
            ->whereNull('order_id')
            ->with(['type', 'fromStation', 'toStation'])
            ->get();

        $totalPendingPoints = 0;
        $cashToPointsRate = (float) Setting::get('cash_to_points_rate', 4);

        foreach ($pendingDeliveries as $delivery) {
            // Get pricing for this route and type
            $pricing = \App\Models\Pricing::where('from_station_id', $delivery->from_station_id)
                ->where('to_station_id', $delivery->to_station_id)
                ->where('type_id', $delivery->type_id)
                ->first();

            if ($pricing) {
                // Calculate points based on price and cash_to_points_rate
                $points = (int) ($pricing->price * $cashToPointsRate);
                $totalPendingPoints += $points;
            }
        }

        return $totalPendingPoints;
    }

    /**
     * Get conversion rates from settings.
     */
    private function getConversionRates(): array
    {
        $cashToPointsRate = Setting::get('cash_to_points_rate', 4);
        $pointsToCashRate = Setting::get('points_to_cash_rate', 5);

        return [
            'cash_to_points_rate' => (float) $cashToPointsRate,
            'points_to_cash_rate' => (float) $pointsToCashRate,
        ];
    }

    /**
     * Convert cash to points (using points_to_cash_rate).
     */
    public function convertCashToPoints(float $cash, ?float $pointsToCashRate = null): int
    {
        if ($pointsToCashRate === null) {
            $pointsToCashRate = (float) Setting::get('points_to_cash_rate', 5);
        }

        return (int) ($cash * $pointsToCashRate);
    }

    /**
     * Convert points to cash (using points_to_cash_rate).
     */
    public function convertPointsToCash(int $points, ?float $pointsToCashRate = null): float
    {
        if ($pointsToCashRate === null) {
            $pointsToCashRate = (float) Setting::get('points_to_cash_rate', 5);
        }

        return round($points / $pointsToCashRate, 2);
    }

    /**
     * Process card purchase (deduct points and create transaction).
     * Handles negative balance scenarios.
     */
    public function processBuyCard(int $userId, string $cardName, int $pointsToDeduct): bool
    {
        $user = User::with('points')->findOrFail($userId);

        // Get total points (current + pending)
        $currentBalance = $user->points->current_balance ?? 0;
        $pendingPoints = $this->getPendingPoints($userId);
        $totalPoints = $currentBalance + $pendingPoints;

        // Check if user has enough total points (considering negative balance)
        if ($totalPoints < $pointsToDeduct) {
            throw new \Exception(__('insufficient_points'));
        }

        // Calculate new balance after deduction
        $newBalance = $currentBalance - $pointsToDeduct;

        // Create transaction
        $transaction = PointTransaction::create([
            'user_id' => $userId,
            'type' => 'spent',
            'amount' => $pointsToDeduct,
            'description' => __('card_purchase').': '.$cardName,
            'balance_after' => $newBalance,
        ]);

        // Update user points balance (can go negative)
        $user->points->update([
            'current_balance' => $newBalance,
            'total_spent' => ($user->points->total_spent ?? 0) + $pointsToDeduct,
        ]);

        return true;
    }

    /**
     * Add points to user account (handles negative balance).
     */
    public function addPoints(int $userId, int $pointsToAdd, string $description, ?int $orderId = null): bool
    {
        $user = User::with('points')->findOrFail($userId);
        $currentBalance = $user->points->current_balance ?? 0;

        // If user has negative balance, deduct from the points being added
        $actualPointsAdded = $pointsToAdd;
        $newBalance = $currentBalance + $pointsToAdd;

        // If current balance is negative, some points will go to cover the debt
        if ($currentBalance < 0) {
            $debtCovered = min(abs($currentBalance), $pointsToAdd);
            $description .= $debtCovered > 0 ? ' (تم تغطية دين: ' . $debtCovered . ' نقطة)' : '';
        }

        // Create transaction
        $transaction = PointTransaction::create([
            'user_id' => $userId,
            'type' => 'earned',
            'amount' => $actualPointsAdded,
            'description' => $description,
            'order_id' => $orderId,
            'balance_after' => $newBalance,
        ]);

        // Update user points balance
        $user->points->update([
            'current_balance' => $newBalance,
            'total_earned' => ($user->points->total_earned ?? 0) + $actualPointsAdded,
        ]);

        return true;
    }

    /**
     * Process order payment with points (deduct points and create transaction).
     * Handles negative balance scenarios.
     */
    public function processOrderPayment(int $userId, string $orderBarcode, int $pointsToDeduct): bool
    {
        $user = User::with('points')->findOrFail($userId);

        // Get total points (current + pending)
        $currentBalance = $user->points->current_balance ?? 0;
        $pendingPoints = $this->getPendingPoints($userId);
        $totalPoints = $currentBalance + $pendingPoints;

        // Check if user has enough total points (considering negative balance)
        if ($totalPoints < $pointsToDeduct) {
            throw new \Exception(__('insufficient_points'));
        }

        // Calculate new balance after deduction
        $newBalance = $currentBalance - $pointsToDeduct;

        // Create transaction with proper description for order payment
        $transaction = PointTransaction::create([
            'user_id' => $userId,
            'type' => 'spent',
            'amount' => $pointsToDeduct,
            'description' => __('order_payment_description', ['barcode' => $orderBarcode]),
            'balance_after' => $newBalance,
        ]);

        // Update user points balance (can go negative)
        $user->points->update([
            'current_balance' => $newBalance,
            'total_spent' => ($user->points->total_spent ?? 0) + $pointsToDeduct,
        ]);

        return true;
    }
}
