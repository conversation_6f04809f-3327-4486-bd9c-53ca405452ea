<?php

namespace App\Services;

use App\Http\Resources\AuthResource;
use App\Models\User;
use App\Models\UserRegistration;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class AuthService
{
    public function register(array $data)
    {
        // البحث عن تسجيل موجود بنفس الهاتف
        $registration = UserRegistration::where('phone', $data['phone'])->first();

        $registrationData = [
            'name' => $data['name'],
            'email' => !empty($data['email']) ? $data['email'] : null,
            'national_id' => $data['national_id'],
            'phone' => $data['phone'],
            'password' => Hash::make($data['password']),
            'verification_code' => 123456, // str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT),
            'code_expires_at' => Carbon::now()->addMinutes(10),
            'is_verified' => false,
        ];

        if ($registration) {
            // تحديث التسجيل الموجود
            $registration->update($registrationData);
        } else {
            // إنشاء تسجيل جديد
            $registration = UserRegistration::create($registrationData);
        }

        // هنا يمكن إرسال الرمز عبر SMS
        // $this->sendSMS($registration->phone, $registration->verification_code);

        return [
            'message' => __('verification_code_sent'),
            'phone' => $registration->phone,
            'expires_at' => $registration->code_expires_at,
        ];
    }

    public function verifyCode($phone, $code)
    {
        try {
            $registration = UserRegistration::where('phone', $phone)
                ->where('verification_code', $code)
                ->first();

            if (!$registration) {
                return ['error' => __('invalid_verification_code')];
            }

            if ($registration->isCodeExpired()) {
                return ['error' => __('verification_code_expired')];
            }

            // إنشاء المستخدم في الجدول الرئيسي
            $user = User::create([
                'name' => $registration->name,
                'email' => $registration->email,
                'national_id' => $registration->national_id,
                'phone' => $registration->phone,
                'password' => $registration->password,
                'phone_verified_at' => Carbon::now(),
            ]);

            // حذف التسجيل من المرحلة المؤقتة
            $registration->delete();

            // إنشاء token للمستخدم
            $token = $user->createToken('auth_token')->plainTextToken;

            return new AuthResource($user, $token, __('registration_successful'));
        } catch (\Exception $e) {
            \Log::error('Error in verifyCode: '.$e->getMessage());

            return ['error' => __('verification_code_error')];
        }
    }

    public function login($phone, $password)
    {
        $user = User::where('phone', $phone)->first();

        if (!$user || !Hash::check($password, $user->password)) {
            return ['error' => __('invalid_credentials')];
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return new AuthResource($user, $token, __('login_successful'));
    }

    public function resendCode($phone)
    {
        $registration = UserRegistration::where('phone', $phone)->first();

        if (!$registration) {
            return ['error' => __('registration_not_found')];
        }

        $registration->generateVerificationCode();

        // هنا يمكن إرسال الرمز عبر SMS
        // $this->sendSMS($registration->phone, $registration->verification_code);

        return [
            'message' => __('verification_code_resent'),
            'expires_at' => $registration->code_expires_at,
        ];
    }

    /**
     * Update user profile.
     *
     * @param array $data
     *
     * @return array
     */
    public function updateProfile($data)
    {
        try {
            $user = auth()->user();

            $user->update([
                'name' => $data['name'] ?? $user->name,
                'email' => $data['email'] ?? $user->email,
                'national_id' => $data['national_id'] ?? $user->national_id,
                'phone' => $data['phone'] ?? $user->phone,
            ]);

            return [
                'user' => $user,
                'message' => __('profile_updated_successfully'),
            ];
        } catch (\Exception $e) {
            \Log::error('Profile update error: '.$e->getMessage());
            throw $e;
        }
    }

    /**
     * Send password reset code via SMS.
     *
     * @param array $data
     *
     * @return array
     */
    public function sendResetCode($data)
    {
        try {
            $user = User::where('phone', $data['phone'])->first();

            if (!$user) {
                return [
                    'status' => false,
                    'message' => __('user_not_found'),
                ];
            }

            // Generate verification code (6 digits)
            $verificationCode = 123456; // str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

            // Store the verification code and its expiry time
            $user->update([
                'verification_code' => $verificationCode,
                'verification_code_expires_at' => now()->addMinutes(10),
            ]);

            // Send SMS with verification code
            $this->sendSMS($user->phone, __('auth.reset_code_message', ['code' => $verificationCode]));

            return [
                'status' => true,
                'message' => __('reset_code_sent'),
                'expires_in' => 10, // minutes
            ];
        } catch (\Exception $e) {
            \Log::error('Send reset code error: '.$e->getMessage());

            return [
                'status' => false,
                'message' => __('failed_to_send_reset_code'),
            ];
        }
    }

    /**
     * Verify the reset code.
     *
     * @param array $data
     *
     * @return array
     */
    public function verifyResetCode($data)
    {
        try {
            $user = User::where('phone', $data['phone'])
                ->where('verification_code', $data['verification_code'])
                ->where('verification_code_expires_at', '>', now())
                ->first();

            if (!$user) {
                return [
                    'status' => false,
                    'message' => __('invalid_or_expired_code'),
                ];
            }

            return [
                'status' => true,
                'message' => __('verification_code_valid'),
            ];
        } catch (\Exception $e) {
            \Log::error('Verify reset code error: '.$e->getMessage());

            return [
                'status' => false,
                'message' => __('verification_code_error'),
            ];
        }
    }

    /**
     * Reset user password using verification code.
     *
     * @param array $data
     *
     * @return array
     */
    public function resetPassword($data)
    {
        // if (!auth()->check()) {
        //     return [
        //         'status' => false,
        //         'message' => __('auth.unauthenticated'),
        //     ];
        // }
        try {
            $user = User::where('phone', $data['phone'])
                ->first();

            if (!$user) {
                return [
                    'status' => false,
                    'message' => __('invalid_or_expired_code'),
                ];
            }

            // Update password
            $user->update([
                'password' => Hash::make($data['password']),
                'verification_code' => null,
                'verification_code_expires_at' => null,
            ]);

            // Invalidate all tokens
            $user->tokens()->delete();

            return [
                'status' => true,
                'message' => __('password_reset_successful'),
                'user' => $user,
            ];
        } catch (\Exception $e) {
            \Log::error('Reset password error: '.$e->getMessage());

            return [
                'status' => false,
                'message' => __('password_reset_error'),
            ];
        }
    }

    private function sendSMS($phone, $code)
    {
        $url = 'http://slng5.com/Api/SendSmsJsonBody.ashx';

        $payload = [
            'Username' => '<EMAIL>',
            'Password' => '69ea553d-faca-40ae-a6f9-d6c14fd61d60',
            'MsgName' => 'DropX Delivery',
            'MsgBody' => $code,
            'FromMobile' => 'Clickwebs',
            'DeliveryAckUrl' => null,
            'Mobiles' => [
                ['Mobile' => $phone],
            ],
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post($url, $payload);

        return $response->successful();
    }
}
