<?php

namespace App\Services;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderDriver;
use App\Models\OrderDriverHistory;
use App\Models\Setting;
use App\Models\Tracking;
use App\Models\User;

class OrderService
{
    /**
     * Create a new order with a unique barcode.
     * Handles payment processing including points deduction.
     *
     * @return Order
     *
     * @throws \Exception
     */
    public function create(array $data)
    {
        // Generate a unique barcode for the order
        $data['barcode'] = $this->generateUniqueBarcode();
        $data['user_id'] = auth()->id();

        // Handle points payment
        if (isset($data['payment_method']) && $data['payment_method'] === 'points') {
            $this->processPointsPayment($data);
        }

        // TODO: Add payment gateway integration for visa payments
        // if ($data['payment_method'] === 'cash') {
        //     $this->processVisaPayment($data);
        // }

        // Create the order
        $order = Order::create($data);

        // Add initial tracking for the new order
        $order->trackings()->create([
            'status' => 'pending',
            'note' => 'Order created',
            'user_id' => auth()->id(),
        ]);

        return $order->load(['fromStation', 'toStation', 'type']);
    }

    /**
     * Process points payment for order.
     */
    private function processPointsPayment(array &$data): void
    {
        $userId = auth()->id();
        $price = (float) $data['price'];

        // Convert price to points
        $pointsToCashRate = (float) Setting::get('points_to_cash_rate', 5);
        $requiredPoints = (int) ($price * $pointsToCashRate);

        // Get user stats to verify points availability
        $userStatsService = app(UserStatsService::class);
        $userStats = $userStatsService->getUserStats($userId);

        // Double check points availability (should be validated in request)
        if ($userStats['total_points'] < $requiredPoints) {
            throw new \Exception(__('insufficient_points_for_order', ['required' => $requiredPoints, 'available' => $userStats['total_points']]));
        }

        // Process points deduction for order payment
        $userStatsService->processOrderPayment(
            $userId,
            $data['barcode'] ?? 'PENDING',
            $requiredPoints
        );

        // Store points used in order
        $data['points_used'] = $requiredPoints;
    }

    public function update(Order $order, array $data)
    {
        $order->update($data);

        return $order;
    }

    public function delete(Order $order)
    {
        return $order->delete();
    }

    public function restore(Order $order)
    {
        return $order->restore();
    }

    public function forceDelete(Order $order)
    {
        return $order->forceDelete();
    }

    public function getAll($perPage = 10, $withTrashed = false, $filters = [])
    {
        $query = Order::with(['fromStation', 'toStation', 'type', 'user', 'latestTracking']);

        if ($withTrashed) {
            $query->onlyTrashed();
        }

        // Apply filters
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['from_station_id'])) {
            $query->where('from_station_id', $filters['from_station_id']);
        }

        if (!empty($filters['to_station_id'])) {
            $query->where('to_station_id', $filters['to_station_id']);
        }

        if (!empty($filters['type_id'])) {
            $query->where('type_id', $filters['type_id']);
        }

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['barcode'])) {
            $query->where('barcode', 'like', '%'.$filters['barcode'].'%');
        }

        if (!empty($filters['receiver_name'])) {
            $query->where('receiver_name', 'like', '%'.$filters['receiver_name'].'%');
        }

        if (!empty($filters['receiver_phone'])) {
            $query->where('receiver_phone', 'like', '%'.$filters['receiver_phone'].'%');
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function findById($id)
    {
        return Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user'])->findOrFail($id);
    }

    public function getByStatus($status, $perPage = 10)
    {
        return Order::with(['fromStation', 'toStation', 'type'])
            ->where('status', $status)
            ->paginate($perPage);
    }

    public function updateStatus(Order $order, $status, $note = null)
    {
        $order->update(['status' => $status]);

        $order->trackings()->create([
            'status' => $status,
            'note' => $note ?? 'Status updated',
            'user_id' => auth()->id(),
        ]);

        if ($status === 'delivered' && $order->orderDriver) {
            $order->orderDriver->update(['completed_at' => now()]);

            // منح النقاط للسائق عند تسليم الطلب
            try {
                $this->givePoint(
                    userId: $order->orderDriver->driver_id,
                    type: 'order_completed',
                    amount: $order->price,
                    description: __('delivery_points_earned', ['barcode' => $order->barcode]),
                    orderId: $order->id
                );
            } catch (\Exception $e) {
                // تسجيل الخطأ في السجلات ولكن لا نوقف العملية
                \Log::error('فشل في منح النقاط للسائق: '.$e->getMessage());
            }
        }

        return $order;
    }

    /**
     * Get order statistics for dashboard.
     */
    public function getOrderStatistics(): array
    {
        $total = Order::count();
        $pending = Order::where('status', 'pending')->count();
        $confirmed = Order::where('status', 'confirmed')->count();
        $pickedUp = Order::where('status', 'picked_up')->count();
        $delivered = Order::where('status', 'delivered')->count();
        $canceled = Order::whereIn('status', [
            'canceled_before_timed_out_by_user',
            'canceled_before_timed_out_by_admin',
            'canceled_timed_out_by_system',
            'canceled_after_confirmed_by_admin',
            'canceled_after_picked_up_by_admin',
            'canceled_after_delivered_by_admin',
        ])->count();

        return [
            'total' => $total,
            'pending' => $pending,
            'confirmed' => $confirmed,
            'picked_up' => $pickedUp,
            'delivered' => $delivered,
            'canceled' => $canceled,
            'active' => $total - $canceled,
        ];
    }

    /**
     * Get orders by status with pagination.
     */
    public function getOrdersByStatus(string $status, int $perPage = 15)
    {
        return Order::with(['fromStation', 'toStation', 'type', 'user', 'latestTracking'])
            ->where('status', $status)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Search orders by multiple criteria.
     */
    public function searchOrders(string $search, int $perPage = 15)
    {
        return Order::with(['fromStation', 'toStation', 'type', 'user', 'latestTracking'])
            ->where(function ($query) use ($search) {
                $query->where('barcode', 'like', "%{$search}%")
                    ->orWhere('receiver_name', 'like', "%{$search}%")
                    ->orWhere('receiver_phone', 'like', "%{$search}%")
                    ->orWhereHas('user', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%");
                    })
                    ->orWhereHas('fromStation', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toStation', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function getCreatedOrders($userId, $filters = [], $perPage = 10)
    {
        $query = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user'])
            ->where('user_id', $userId);

        if (!empty($filters['from_station_id'])) {
            $query->where('from_station_id', $filters['from_station_id']);
        }

        if (!empty($filters['to_station_id'])) {
            $query->where('to_station_id', $filters['to_station_id']);
        }

        if (!empty($filters['type_id'])) {
            $query->where('type_id', $filters['type_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function getReceivedOrders($userId, $filters = [], $perPage = 10)
    {
        // الطلبات التي قام بتوصيلها (مع معرف طلب)
        $orders = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user', 'orderDriver.driver'])->where('status', 'picked_up')
            ->whereHas('orderDriver', function ($q) use ($userId) {
                $q->where('driver_id', $userId)->whereNotNull('order_id');
            })
            ->orderBy('created_at', 'desc')
            ->get();

        // الاهتمامات بدون طلبات (بدون معرف طلب)
        $interests = OrderDriver::with(['type', 'fromStation', 'toStation'])
            ->where('driver_id', $userId)
            ->whereNull('order_id')
            ->orderBy('created_at', 'desc')
            ->get();

        // دمج النتائج
        $combined = collect();

        // إضافة الطلبات
        foreach ($orders as $order) {
            $combined->push($order);
        }

        // إضافة الاهتمامات
        foreach ($interests as $interest) {
            $combined->push($interest);
        }

        return $combined->sortByDesc('created_at');
    }

    public function searchAvailableDeliveries($userId, $fromStationId, $toStationId)
    {
        $availableOrders = Order::with('type')
            ->where('user_id', '!=', $userId)
            ->where('status', 'confirmed')
            ->where('from_station_id', $fromStationId)
            ->where('to_station_id', $toStationId)
            ->whereDoesntHave('orderDriver')
            ->get()
            ->groupBy('type.id');

        $result = [];
        foreach ($availableOrders as $typeId => $orders) {
            // حساب عدد الأشخاص المهتمين من جدول attendances
            $attendanceCount = \App\Models\Attendance::where('type_id', $typeId)
                ->where('from_station_id', $fromStationId)
                ->where('to_station_id', $toStationId)
                ->count();

            // حساب عدد السائقين المسجلين في order_drivers بدون order_id
            $orderDriversCount = OrderDriver::where('type_id', $typeId)
                ->where('from_station_id', $fromStationId)
                ->where('to_station_id', $toStationId)
                ->whereNull('order_id')
                ->count();

            // إجمالي المهتمين = attendances + order_drivers بدون order_id
            $interestedCount = $attendanceCount + $orderDriversCount;

            // حساب النسبة المئوية
            $ordersCount = $orders->count();
            $deliveryChance = $ordersCount > $interestedCount
                ? 100 : ($interestedCount > 0 ? round(($ordersCount / $interestedCount) * 100, 1) : 100);

            $result[] = [
                'type_name' => $orders->first()->type->name,
                'shipments_count' => $orders->count(),
                'interested_people' => $interestedCount,
                'delivery_chance' => $deliveryChance.'%',
            ];
        }

        return $result;
    }

    public function registerInterest($driverId, $typeId, $fromStationId, $toStationId)
    {
        // تسجيل اهتمام السائق بالنوع والمحطات
        return OrderDriver::create([
            'driver_id' => $driverId,
            'type_id' => $typeId,
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
            'assigned_at' => now(),
        ]);
    }

    public function assignRandomOrder($interestId)
    {
        $interest = OrderDriver::findOrFail($interestId);

        // اختيار طلب عشوائي من الطلبات المتاحة
        $order = Order::where('user_id', '!=', $interest->driver_id)
            ->where('type_id', $interest->type_id)
            ->where('from_station_id', $interest->from_station_id)
            ->where('to_station_id', $interest->to_station_id)
            ->where('status', 'confirmed')
            ->whereDoesntHave('orderDriver')
            ->inRandomOrder()
            ->first();

        if (!$order) {
            throw new \Exception(__('no_available_orders'));
        }

        // ربط الاهتمام بالطلب
        $interest->update(['order_id' => $order->id]);

        // تحديث حالة الطلب والتراكنج
        $this->updateStatus($order, 'picked_up', 'Order picked up by driver');

        return $order->fresh(['fromStation', 'toStation', 'type', 'latestTracking.user', 'orderDriver.driver']);
    }

    /**
     * Get user order history with optional status filtering.
     *
     * @param int         $userId
     * @param string|null $status Filter by order status (e.g., 'pending', 'confirmed', 'picked_up', 'delivered', 'cancelled')
     *
     * @return array
     */
    public function getUserHistory($userId, ?string $status = null)
    {
        // Base query for created orders
        $createdQuery = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user'])
            ->where('user_id', $userId);

        // Apply status filter if provided
        if ($status) {
            $createdQuery->where('status', $status);
        }

        $createdOrders = $createdQuery->orderBy('created_at', 'desc')->get();

        // Base query for delivered orders
        $deliveredQuery = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user', 'orderDriver.driver'])
            ->whereHas('orderDriver', function ($q) use ($userId) {
                $q->where('driver_id', $userId);
            });

        // Apply status filter if provided
        if ($status) {
            $deliveredQuery->where('status', $status);
        }

        $deliveredOrders = $deliveredQuery->orderBy('created_at', 'desc')->get();

        return [
            'created' => $createdOrders,
            'delivered' => $deliveredOrders,
        ];
    }

    /**
     * Add points to user's account.
     *
     * @throws \Exception
     */
    /**
     * Generate a unique 6-digit barcode for orders.
     *
     * @throws \Exception
     */
    private function generateUniqueBarcode(): string
    {
        $maxAttempts = 100; // Prevent infinite loops
        $attempt = 0;

        do {
            // Generate a random 6-digit number
            $barcode = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);

            // Check if barcode already exists
            $exists = Order::where('barcode', $barcode)->exists();

            if (!$exists) {
                return $barcode;
            }

            ++$attempt;
        } while ($attempt < $maxAttempts);

        throw new \Exception('Unable to generate a unique barcode after '.$maxAttempts.' attempts');
    }

    /**
     * Add points to user's account.
     *
     * @param int      $userId      The ID of the user to add points to
     * @param string   $type        The type of points (e.g., 'order_completed')
     * @param float    $amount      The monetary amount to calculate points from
     * @param string   $description Description of the points transaction
     * @param int|null $orderId     Optional order ID associated with the points
     *
     * @throws \Exception
     */
    private function givePoint(int $userId, string $type, float $amount, string $description, ?int $orderId = null): array
    {
        // Get points rate from settings
        $pointsRate = Setting::where('key', 'cash_to_points_rate')->first();
        if (!$pointsRate || !is_numeric($pointsRate->value)) {
            throw new \Exception(__('invalid_points_rate'));
        }

        // Calculate points to add
        $cashToPointsRate = (float) $pointsRate->value;
        $pointsToAdd = (int) ($amount * $cashToPointsRate);

        if ($pointsToAdd <= 0) {
            throw new \Exception(__('invalid_points_amount'));
        }

        // Use UserStatsService to add points (handles negative balance properly)
        $userStatsService = app(UserStatsService::class);
        $success = $userStatsService->addPoints(
            $userId,
            $pointsToAdd,
            $description,
            $orderId
        );

        if (!$success) {
            throw new \Exception(__('failed_to_add_points'));
        }

        // Get updated user stats
        $userStats = $userStatsService->getUserStats($userId);

        return [
            'success' => true,
            'points_added' => $pointsToAdd,
            'new_balance' => $userStats['current_balance'],
            'total_points' => $userStats['total_points'],
        ];
    }

    /*
     * Generate a unique 6-digit barcode for orders.
     *
     * @throws \Exception
     */
    // public function generateUniqueBarcode(): string
    // {
    //     $maxAttempts = 100; // Prevent infinite loops
    //     $attempt = 0;

    //     do {
    //         // Generate a random 6-digit number
    //         $barcode = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);

    //         // Check if barcode already exists
    //         $exists = Order::where('barcode', $barcode)->exists();

    //         if (!$exists) {
    //             return $barcode;
    //         }

    //         ++$attempt;
    //     } while ($attempt < $maxAttempts);

    //     throw new \Exception('Unable to generate a unique barcode after '.$maxAttempts.' attempts');
    // }

    /**
     * Get stuck orders (older than 48 hours and not delivered).
     */
    public function getStuckOrders($perPage = 15)
    {
        return Order::where('created_at', '<=', now()->subHours(48))
            ->whereIn('status', OrderStatus::getStuckOrderStatuses())
            ->with(['user', 'fromStation', 'toStation', 'type', 'orderDriver.driver'])
            ->orderBy('created_at', 'asc')
            ->paginate($perPage);
    }

    /**
     * Get stuck orders count.
     */
    public function getStuckOrdersCount(): int
    {
        return Order::where('created_at', '<=', now()->subHours(48))
            ->whereIn('status', OrderStatus::getStuckOrderStatuses())
            ->count();
    }

    /**
     * Get dashboard statistics.
     */
    public function getDashboardStatistics(): array
    {
        $totalOrders = Order::count();
        $pendingOrders = Order::where('status', OrderStatus::PENDING->value)->count();
        $confirmedOrders = Order::where('status', OrderStatus::CONFIRMED->value)->count();
        $pickedUpOrders = Order::where('status', OrderStatus::PICKED_UP->value)->count();
        $deliveredOrders = Order::where('status', OrderStatus::DELIVERED->value)->count();
        $canceledOrders = Order::whereIn('status', [
            OrderStatus::CANCELED_BEFORE_TIMED_OUT_BY_USER->value,
            OrderStatus::CANCELED_BEFORE_TIMED_OUT_BY_ADMIN->value,
            OrderStatus::CANCELED_TIMED_OUT_BY_SYSTEM->value,
            OrderStatus::CANCELED_AFTER_CONFIRMED_BY_ADMIN->value,
            OrderStatus::CANCELED_AFTER_PICKED_UP_BY_ADMIN->value,
            OrderStatus::CANCELED_AFTER_DELIVERED_BY_ADMIN->value,
        ])->count();
        $stuckOrders = $this->getStuckOrdersCount();

        $todayOrders = Order::whereDate('created_at', today())->count();
        $thisWeekOrders = Order::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count();
        $thisMonthOrders = Order::whereMonth('created_at', now()->month)->count();

        $totalRevenue = Order::where('status', OrderStatus::DELIVERED->value)->sum('price');
        $todayRevenue = Order::where('status', OrderStatus::DELIVERED->value)
            ->whereDate('created_at', today())
            ->sum('price');

        return [
            'total_orders' => $totalOrders,
            'pending_orders' => $pendingOrders,
            'confirmed_orders' => $confirmedOrders,
            'picked_up_orders' => $pickedUpOrders,
            'delivered_orders' => $deliveredOrders,
            'canceled_orders' => $canceledOrders,
            'stuck_orders' => $stuckOrders,
            'today_orders' => $todayOrders,
            'this_week_orders' => $thisWeekOrders,
            'this_month_orders' => $thisMonthOrders,
            'total_revenue' => $totalRevenue,
            'today_revenue' => $todayRevenue,
            'delivery_rate' => $totalOrders > 0 ? round(($deliveredOrders / $totalOrders) * 100, 2) : 0,
        ];
    }

    /**
     * Cancel orders that have exceeded their delivery deadline.
     */
    public function cancelExpiredOrders(): int
    {
        // Get delivery deadline setting (in minutes)
        $deliveryDeadlineMinutes = (int) Setting::get('delivery_deadline_minutes', 30);

        // Calculate the cutoff time
        $cutoffTime = now()->subMinutes($deliveryDeadlineMinutes);

        // Find pending orders that have exceeded the deadline
        $expiredOrders = Order::where('status', OrderStatus::PENDING->value)
            ->where('created_at', '<=', $cutoffTime)
            ->get();

        $canceledCount = 0;

        foreach ($expiredOrders as $order) {
            try {
                // Update order status to canceled_timed_out_by_system
                $this->updateStatus(
                    $order,
                    OrderStatus::CANCELED_TIMED_OUT_BY_SYSTEM->value,
                    'تم إلغاء الطلب تلقائياً بسبب انتهاء الوقت المحدد للتسليم'
                );

                ++$canceledCount;

                \Log::info("تم إلغاء الطلب رقم {$order->barcode} تلقائياً بسبب انتهاء الوقت المحدد");
            } catch (\Exception $e) {
                \Log::error("فشل في إلغاء الطلب رقم {$order->barcode}: ".$e->getMessage());
            }
        }

        return $canceledCount;
    }

    /**
     * Clean expired driver interests (order_drivers with null order_id).
     */
    public function cleanExpiredDriverInterests(): int
    {
        // Get delivery deadline setting (in minutes)
        $deliveryDeadlineMinutes = (int) Setting::get('delivery_deadline_minutes', 30);

        // Calculate the cutoff time
        $cutoffTime = now()->subMinutes($deliveryDeadlineMinutes);

        // Find order_drivers records with null order_id that have exceeded the deadline
        $expiredInterests = OrderDriver::whereNull('order_id')
            ->where('created_at', '<=', $cutoffTime)
            ->get();

        $deletedCount = 0;

        foreach ($expiredInterests as $interest) {
            try {
                \Log::info("حذف اهتمام السائق منتهي الصلاحية - السائق: {$interest->driver_id}, النوع: {$interest->type_id}, من: {$interest->from_station_id}, إلى: {$interest->to_station_id}");

                // Save to history before deleting
                $this->saveOrderDriverToHistory($interest, 'expired_by_system', 'تم الإلغاء تلقائياً بسبب انتهاء الوقت المحدد');

                $interest->delete();
                ++$deletedCount;
            } catch (\Exception $e) {
                \Log::error("فشل في حذف اهتمام السائق رقم {$interest->id}: ".$e->getMessage());
            }
        }

        return $deletedCount;
    }

    /**
     * Save OrderDriver record to history before deletion.
     */
    private function saveOrderDriverToHistory(OrderDriver $orderDriver, string $reason, ?string $note = null): void
    {
        OrderDriverHistory::create([
            'original_order_driver_id' => $orderDriver->id,
            'order_id' => $orderDriver->order_id,
            'driver_id' => $orderDriver->driver_id,
            'type_id' => $orderDriver->type_id,
            'from_station_id' => $orderDriver->from_station_id,
            'to_station_id' => $orderDriver->to_station_id,
            'assigned_at' => $orderDriver->assigned_at,
            'completed_at' => $orderDriver->completed_at,
            'notes' => $orderDriver->notes,
            'cancellation_reason' => $reason,
            'cancellation_note' => $note,
            'cancelled_at' => now(),
            'original_created_at' => $orderDriver->created_at,
            'original_updated_at' => $orderDriver->updated_at,
        ]);
    }

    /**
     * Cancel driver interest manually (by driver or admin).
     */
    public function cancelDriverInterest(int $orderDriverId, int $driverId, string $reason = 'cancelled_by_driver', ?string $note = null): bool
    {
        $orderDriver = OrderDriver::where('id', $orderDriverId)
            ->where('driver_id', $driverId)
            ->whereNull('order_id') // Only interests without assigned orders
            ->first();

        if (!$orderDriver) {
            throw new \Exception('اهتمام السائق غير موجود أو لا يمكن إلغاؤه');
        }

        // Check if still within allowed time
        $deliveryDeadlineMinutes = (int) Setting::get('delivery_deadline_minutes', 30);
        $cutoffTime = now()->subMinutes($deliveryDeadlineMinutes);

        if ($orderDriver->created_at <= $cutoffTime) {
            throw new \Exception('لا يمكن إلغاء الاهتمام بعد انتهاء الوقت المسموح');
        }

        try {
            // Save to history before deleting
            $this->saveOrderDriverToHistory($orderDriver, $reason, $note);

            // Delete the original record
            $orderDriver->delete();

            \Log::info("تم إلغاء اهتمام السائق رقم {$orderDriverId} من قبل السائق {$driverId}");

            return true;
        } catch (\Exception $e) {
            \Log::error("فشل في إلغاء اهتمام السائق رقم {$orderDriverId}: ".$e->getMessage());
            throw $e;
        }
    }

    /**
     * Cancel order by owner before deadline
     */
    public function cancelOrderByOwner(int $orderId, int $userId, string $note = null): bool
    {
        $order = Order::where('id', $orderId)
            ->where('user_id', $userId)
            ->where('status', OrderStatus::PENDING->value)
            ->first();

        if (!$order) {
            throw new \Exception('الطلب غير موجود أو لا يمكن إلغاؤه');
        }

        // Check if still within allowed time
        $deliveryDeadlineMinutes = (int) Setting::get('delivery_deadline_minutes', 30);
        $cutoffTime = now()->subMinutes($deliveryDeadlineMinutes);

        if ($order->created_at <= $cutoffTime) {
            throw new \Exception('لا يمكن إلغاء الطلب بعد انتهاء الوقت المسموح');
        }

        try {
            // Check if there's an assigned driver and save to history
            $orderDriver = OrderDriver::where('order_id', $orderId)->first();
            if ($orderDriver) {
                $this->saveOrderDriverToHistory($orderDriver, 'cancelled_by_admin', 'تم إلغاء الطلب من قبل صاحب الطلب: ' . ($note ?? ''));
                $orderDriver->delete();
            }

            // Update order status
            $this->updateStatus(
                $order,
                OrderStatus::CANCELED_BEFORE_TIMED_OUT_BY_USER->value,
                'تم إلغاء الطلب من قبل صاحب الطلب' . ($note ? ': ' . $note : '')
            );

            // Refund points if payment was made with points
            if ($order->payment_method === 'points' && $order->points_used > 0) {
                $this->givePoint(
                    userId: $userId,
                    type: 'order_cancelled_refund',
                    amount: $order->points_used,
                    description: __('order_cancelled_points_refund', ['barcode' => $order->barcode]),
                    orderId: $order->id
                );
            }

            \Log::info("تم إلغاء الطلب رقم {$order->barcode} من قبل صاحب الطلب {$userId}");

            return true;
        } catch (\Exception $e) {
            \Log::error("فشل في إلغاء الطلب رقم {$orderId}: " . $e->getMessage());
            throw $e;
        }
    }
}
