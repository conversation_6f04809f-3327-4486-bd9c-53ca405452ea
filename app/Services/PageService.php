<?php

namespace App\Services;

use App\Models\Page;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;

class PageService
{
    public function create(array $data): Page
    {
        // إنشاء slug تلقائياً إذا لم يتم توفيره
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateSlug($data['title']);
        } else {
            $data['slug'] = $this->generateSlug($data['slug']);
        }

        return Page::create($data);
    }

    public function update(Page $page, array $data): Page
    {
        // تحديث slug إذا تم تغيير العنوان أو slug
        if (isset($data['title']) || isset($data['slug'])) {
            $newSlug = $data['slug'] ?? $data['title'];
            $data['slug'] = $this->generateSlug($newSlug, $page->id);
        }

        $page->update($data);

        return $page->fresh();
    }

    public function delete(Page $page): bool
    {
        return $page->delete();
    }

    public function restore(Page $page): bool
    {
        return $page->restore();
    }

    public function forceDelete(Page $page): bool
    {
        return $page->forceDelete();
    }

    public function getAll(int $perPage = 10, bool $withTrashed = false, ?string $search = null, ?string $status = null, ?bool $featured = null, string $sortBy = 'created_at', string $sortOrder = 'desc'): LengthAwarePaginator
    {
        $query = Page::with(['creator', 'updater']);

        if ($withTrashed) {
            $query->withTrashed();
        }

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($status) {
            $query->where('status', $status);
        }

        // Apply featured filter
        if ($featured !== null) {
            $query->where('is_featured', $featured);
        }

        return $query->orderBy($sortBy, $sortOrder)
                    ->paginate($perPage);
    }

    public function getPublished(int $perPage = 10, ?string $search = null, ?bool $featured = null): LengthAwarePaginator
    {
        $query = Page::published()->ordered();

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Apply featured filter
        if ($featured !== null) {
            $query->where('is_featured', $featured);
        }

        return $query->paginate($perPage);
    }

    public function getFeatured(int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        return Page::published()
                   ->featured()
                   ->ordered()
                   ->limit($limit)
                   ->get();
    }

    public function findById(int $id, bool $withTrashed = false): Page
    {
        $query = Page::with(['creator', 'updater']);

        if ($withTrashed) {
            $query->withTrashed();
        }

        return $query->findOrFail($id);
    }

    public function findBySlug(string $slug): ?Page
    {
        return Page::where('slug', $slug)->first();
    }

    public function publish(Page $page): bool
    {
        return $page->publish();
    }

    public function unpublish(Page $page): bool
    {
        return $page->unpublish();
    }

    public function archive(Page $page): bool
    {
        return $page->archive();
    }

    public function toggleFeatured(Page $page): bool
    {
        return $page->update([
            'is_featured' => !$page->is_featured,
        ]);
    }

    public function updateSortOrder(array $pageIds): bool
    {
        foreach ($pageIds as $index => $pageId) {
            Page::where('id', $pageId)->update([
                'sort_order' => $index + 1,
            ]);
        }

        return true;
    }

    public function search(string $query, int $perPage = 10, bool $publishedOnly = false): LengthAwarePaginator
    {
        $queryBuilder = Page::where(function ($q) use ($query) {
            $q->where('title', 'like', "%{$query}%")
              ->orWhere('content', 'like', "%{$query}%")
              ->orWhere('excerpt', 'like', "%{$query}%");
        });

        if ($publishedOnly) {
            $queryBuilder->published();
        }

        return $queryBuilder->ordered()
                           ->paginate($perPage);
    }

    public function getByStatus(string $status, int $perPage = 10): LengthAwarePaginator
    {
        return Page::where('status', $status)
                   ->with(['creator', 'updater'])
                   ->ordered()
                   ->paginate($perPage);
    }

    public function duplicate(Page $page): Page
    {
        $newPage = $page->replicate();
        $newPage->title = $newPage->title.' (Copy)';
        $newPage->slug = $this->generateSlug($newPage->title);
        $newPage->status = 'draft';
        $newPage->published_at = null;
        $newPage->save();

        return $newPage;
    }

    protected function generateSlug(string $title, ?int $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $originalSlug.'-'.$counter;
            ++$counter;
        }

        return $slug;
    }

    protected function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = Page::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    public function getStatusOptions(): array
    {
        return [
            'draft' => __('Draft'),
            'published' => __('Published'),
            'archived' => __('Archived'),
        ];
    }

    public function getStatusCounts(): array
    {
        return [
            'all' => Page::count(),
            'published' => Page::where('status', 'published')->count(),
            'draft' => Page::where('status', 'draft')->count(),
            'archived' => Page::where('status', 'archived')->count(),
            'trashed' => Page::onlyTrashed()->count(),
        ];
    }
}
