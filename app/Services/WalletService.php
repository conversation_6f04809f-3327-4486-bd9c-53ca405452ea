<?php

namespace App\Services;

use App\Models\PointTransaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class WalletService
{
    /**
     * Get user's wallet information.
     */
    public function getUserWallet(int $userId): array
    {
        $user = User::with('points')->findOrFail($userId);
        $userPoints = $user->points;

        return [
            'balance' => $userPoints->current_balance ?? 0,
            'total_earned' => $this->getTotalPoints($userId, 'earned'),
            'total_spent' => $this->getTotalPoints($userId, 'spent'),
            'transactions_count' => PointTransaction::where('user_id', $userId)->count(),
        ];
    }

    /**
     * Get user's point transactions with filters.
     *
     * @param string|null $type     Transaction type filter ('earned' or 'spent')
     * @param string|null $search   Search term for description or order barcode/id
     * @param string|null $dateFrom Start date for filtering (Y-m-d)
     * @param string|null $dateTo   End date for filtering (Y-m-d)
     */
    public function getUserTransactions(
        int $userId,
        int $perPage = 15,
        ?string $type = null,
        ?string $search = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): LengthAwarePaginator {
        $query = PointTransaction::with(['order'])
            ->where('user_id', $userId);

        // Filter by transaction type
        if (in_array($type, ['earned', 'spent'])) {
            $query->where('type', $type);
        }

        // Search in description or order barcode/id
        if ($search) {
            $query->where(function (Builder $q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhereHas('order', function ($q) use ($search) {
                      $q->where('barcode', 'like', "%{$search}%")
                        ->orWhere('id', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by date range
        if ($dateFrom) {
            $query->whereDate('created_at', '>=', Carbon::parse($dateFrom)->startOfDay());
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        return $query->latest()
            ->paginate($perPage);
    }

    /**
     * Get all transactions for admin with filters.
     *
     * @param string|null $type     Transaction type filter ('earned' or 'spent')
     * @param string|null $search   Search term for description, order barcode/id, or user name
     * @param string|null $dateFrom Start date for filtering (Y-m-d)
     * @param string|null $dateTo   End date for filtering (Y-m-d)
     * @param int|null    $userId   Filter by specific user ID
     */
    public function getAllTransactions(
        int $perPage = 15,
        ?string $type = null,
        ?string $search = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?int $userId = null
    ): LengthAwarePaginator {
        $query = PointTransaction::with(['order', 'user']);

        // Filter by user if specified
        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Filter by transaction type
        if (in_array($type, ['earned', 'spent'])) {
            $query->where('type', $type);
        }

        // Search in description, order barcode/id, or user name
        if ($search) {
            $query->where(function (Builder $q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhereHas('order', function ($q) use ($search) {
                      $q->where('barcode', 'like', "%{$search}%")
                        ->orWhere('id', 'like', "%{$search}%");
                  })
                  ->orWhereHas('user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by date range
        if ($dateFrom) {
            $query->whereDate('created_at', '>=', Carbon::parse($dateFrom)->startOfDay());
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        return $query->latest()
            ->paginate($perPage);
    }

    /**
     * Get wallet statistics for admin dashboard.
     */
    public function getAdminWalletStats(): array
    {
        $totalEarned = PointTransaction::where('type', 'earned')->sum('amount');
        $totalSpent = PointTransaction::where('type', 'spent')->sum('amount');
        $totalBalance = $totalEarned - $totalSpent;
        $totalTransactions = PointTransaction::count();
        $totalUsers = User::whereHas('points')->count();

        return [
            'total_balance' => $totalBalance,
            'total_earned' => $totalEarned,
            'total_spent' => $totalSpent,
            'total_transactions' => $totalTransactions,
            'total_users' => $totalUsers,
        ];
    }

    /**
     * Get transaction details with order information.
     */
    public function getTransactionWithOrder(int $userId, int $transactionId): PointTransaction
    {
        return PointTransaction::with(['order', 'order.fromStation', 'order.toStation', 'order.type'])
            ->where('user_id', $userId)
            ->findOrFail($transactionId);
    }

    /**
     * Calculate total points for a user by type.
     */
    private function getTotalPoints(int $userId, string $type): int
    {
        return PointTransaction::where('user_id', $userId)
            ->where('type', $type)
            ->sum('amount');
    }
}
