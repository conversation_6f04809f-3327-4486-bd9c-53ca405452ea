<?php

namespace App\Services;

use App\Models\Pricing;

class PricingService
{
    public function create(array $data)
    {
        return Pricing::create($data);
    }

    public function update(Pricing $pricing, array $data)
    {
        $pricing->update($data);
        return $pricing;
    }

    public function delete(Pricing $pricing)
    {
        return $pricing->delete();
    }

    public function getAll($perPage = 10, $withTrashed = false)
    {
        $query = Pricing::select('from_station_id', 'to_station_id')
            ->selectRaw('COUNT(*) as types_count')
            ->selectRaw('MAX(deleted_at) as deleted_at')
            ->with(['fromStation', 'toStation'])
            ->groupBy('from_station_id', 'to_station_id');
        
        if ($withTrashed) {
            $query->onlyTrashed();
        }
        
        return $query->paginate($perPage);
    }

    public function findById($id)
    {
        return Pricing::withTrashed()->with(['fromStation', 'toStation', 'type'])->findOrFail($id);
    }

    public function restore(Pricing $pricing)
    {
        return $pricing->restore();
    }

    public function forceDelete(Pricing $pricing)
    {
        return $pricing->forceDelete();
    }

    public function deleteRoute($fromStationId, $toStationId)
    {
        return Pricing::where([
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
        ])->delete();
    }

    public function restoreRoute($fromStationId, $toStationId)
    {
        return Pricing::withTrashed()->where([
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
        ])->restore();
    }

    public function forceDeleteRoute($fromStationId, $toStationId)
    {
        return Pricing::withTrashed()->where([
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
        ])->forceDelete();
    }

    public function getRouteExists($fromStationId, $toStationId)
    {
        return Pricing::where([
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
        ])->exists();
    }

    public function getRoutePrices($fromStationId, $toStationId)
    {
        return Pricing::where([
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
        ])->get();
    }

    public function updateOrCreatePrice($fromStationId, $toStationId, $typeId, $price)
    {
        return Pricing::updateOrCreate(
            [
                'from_station_id' => $fromStationId,
                'to_station_id' => $toStationId,
                'type_id' => $typeId,
            ],
            ['price' => $price]
        );
    }

    public function deletePrice($fromStationId, $toStationId, $typeId)
    {
        return Pricing::where([
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
            'type_id' => $typeId,
        ])->forceDelete();
    }

    public function findByRoute($fromStationId, $toStationId, $typeId)
    {
        return Pricing::with(['fromStation', 'toStation', 'type'])
            ->where([
                'from_station_id' => $fromStationId,
                'to_station_id' => $toStationId,
                'type_id' => $typeId,
            ])->first();
    }
}