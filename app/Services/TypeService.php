<?php

namespace App\Services;

use App\Models\Type;

class TypeService
{
    public function create(array $data)
    {
        return Type::create($data);
    }

    public function update(Type $type, array $data)
    {
        $type->update($data);
        return $type;
    }

    public function delete(Type $type)
    {
        return $type->delete();
    }

    public function getAll($perPage = 10, $withTrashed = false)
    {
        $query = Type::query();
        if ($withTrashed) {
            $query->onlyTrashed();
        }
        return $query->paginate($perPage);
    }

    public function restore(Type $type)
    {
        return $type->restore();
    }

    public function forceDelete(Type $type)
    {
        return $type->forceDelete();
    }

    public function findById($id)
    {
        return Type::withTrashed()->findOrFail($id);
    }
}