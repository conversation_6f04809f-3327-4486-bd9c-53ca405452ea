<?php

namespace App\Services;

use App\Models\Attendance;

class AttendanceService
{
    public function register($userId, $typeId, $fromStationId, $toStationId)
    {
        return Attendance::updateOrCreate(
            ['user_id' => $userId],
            [
                'type_id' => $typeId,
                'from_station_id' => $fromStationId,
                'to_station_id' => $toStationId,
            ]
        );
    }

    public function remove($userId)
    {
        return Attendance::where('user_id', $userId)->delete();
    }
}