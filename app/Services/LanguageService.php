<?php

namespace App\Services;

use App\Models\Language;

class LanguageService
{
    public function getAllLanguages($perPage = 10)
    {
        return Language::paginate($perPage);
    }

    public function getLanguageById($id)
    {
        return Language::findOrFail($id);
    }

    public function createLanguage(array $data)
    {
        return Language::create($data);
    }

    public function updateLanguage($id, array $data)
    {
        $language = Language::findOrFail($id);
        $language->update($data);
        return $language;
    }

    public function deleteLanguage($id)
    {
        $language = Language::findOrFail($id);
        return $language->delete();
    }
}