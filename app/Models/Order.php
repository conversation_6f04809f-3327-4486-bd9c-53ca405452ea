<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'from_station_id',
        'to_station_id',
        'type_id',
        'receiver_name',
        'receiver_phone',
        'note',
        'price',
        'status',
        'payment_method',
        // 'points',
        'points_used',
        'delivery_deadline',
        'barcode',
    ];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    public function fromStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'from_station_id');
    }

    public function toStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'to_station_id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(Type::class);
    }

    public function trackings(): HasMany
    {
        return $this->hasMany(Tracking::class);
    }

    public function latestTracking()
    {
        return $this->hasOne(Tracking::class)->latest();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function orderDriver()
    {
        return $this->hasOne(OrderDriver::class);
    }
}
