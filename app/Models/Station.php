<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Station extends Model
{
    use SoftDeletes;
    
    protected $fillable = ['name', 'latitude', 'longitude'];

    public function fromOrders()
    {
        return $this->hasMany(Order::class, 'from_station_id');
    }

    public function toOrders()
    {
        return $this->hasMany(Order::class, 'to_station_id');
    }

    public function trackings()
    {
        return $this->hasMany(Tracking::class);
    }
}