<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Translation extends Model
{
    protected $fillable = [
        'key',
        'value',
        'language_code'
    ];

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_code', 'code');
    }

    public static function get($key, $languageCode = null)
    {
        $languageCode = $languageCode ?? app()->getLocale();
        
        $translation = static::where('key', $key)
            ->where('language_code', $languageCode)
            ->first();

        return $translation ? $translation->value : $key;
    }
}