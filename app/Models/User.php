<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use Notifiable;
    use HasRoles;
    use HasApiTokens;
    use HasProfilePhoto;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'national_id',
        'phone',
        'password',
        'phone_verified_at',
        'verification_code',
        'verification_code_expires_at',
        'profile_photo_path',
        'profile_photo_disk',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function trackings()
    {
        return $this->hasMany(Tracking::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function points()
    {
        return $this->hasOne(UserPoint::class);
    }

    public function driverOrders()
    {
        return $this->hasMany(OrderDriver::class, 'driver_id');
    }
}
