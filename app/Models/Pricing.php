<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Pricing extends Model
{
    use SoftDeletes;
    
    protected $fillable = [
        'from_station_id',
        'to_station_id', 
        'type_id',
        'price'
    ];

    protected $casts = [
        'price' => 'decimal:2'
    ];

    public function fromStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'from_station_id');
    }

    public function toStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'to_station_id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(Type::class);
    }
}