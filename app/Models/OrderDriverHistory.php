<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderDriverHistory extends Model
{
    use HasFactory;

    protected $table = 'order_drivers_history';

    protected $fillable = [
        'original_order_driver_id',
        'order_id',
        'driver_id',
        'type_id',
        'from_station_id',
        'to_station_id',
        'assigned_at',
        'completed_at',
        'notes',
        'cancellation_reason',
        'cancellation_note',
        'cancelled_at',
        'original_created_at',
        'original_updated_at',
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'original_created_at' => 'datetime',
        'original_updated_at' => 'datetime',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'driver_id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(Type::class);
    }

    public function fromStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'from_station_id');
    }

    public function toStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'to_station_id');
    }

    /**
     * Get the cancellation reason label
     */
    public function getCancellationReasonLabelAttribute(): string
    {
        return match ($this->cancellation_reason) {
            'expired_by_system' => 'تم الإلغاء تلقائياً بسبب انتهاء الوقت',
            'cancelled_by_driver' => 'تم الإلغاء من قبل السائق',
            'cancelled_by_admin' => 'تم الإلغاء من قبل الإدارة',
            default => 'غير محدد',
        };
    }
}
