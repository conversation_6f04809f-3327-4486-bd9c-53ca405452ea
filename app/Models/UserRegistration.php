<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class UserRegistration extends Model
{
    protected $fillable = [
        'name',
        'email',
        'national_id',
        'phone',
        'password',
        'verification_code',
        'code_expires_at',
        'is_verified',
    ];

    protected $casts = [
        'code_expires_at' => 'datetime',
        'is_verified' => 'boolean',
    ];

    public function isCodeExpired()
    {
        return Carbon::now()->isAfter($this->code_expires_at);
    }

    public function generateVerificationCode()
    {
        $this->verification_code = 123456;
        // $this->verification_code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $this->code_expires_at = Carbon::now()->addMinutes(10);
        $this->save();

        return $this->verification_code;
    }
}
